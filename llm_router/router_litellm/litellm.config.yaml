general_settings:
  debug_level: DEBUG
  set_verbose: true
  timeout: 30
litellm_settings:
  cache: true
  cache_params:
    type: local
model_list:
- litellm_model_name: litellm_proxy/qwen2.5-1.5b-instruct-q4km-1
  litellm_params:
    api_base: http://gpustack-server:80/v1
    api_key: gpustack_643730d961b0a990_f2107549c86b1dc42fba04c1fc983ffa
    model: litellm_proxy/qwen2.5-1.5b-instruct-q4km-1
  model_info:
    id: instruct-endpoint-1
    path: /models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf
  model_name: chat_instruct
  provider: litellm_proxy
- litellm_model_name: litellm_proxy/qwen2.5-1.5b-instruct-q4km-2
  litellm_params:
    api_base: http://gpustack-server:80/v1
    api_key: gpustack_643730d961b0a990_f2107549c86b1dc42fba04c1fc983ffa
    model: litellm_proxy/qwen2.5-1.5b-instruct-q4km-2
  model_info:
    id: instruct-endpoint-2
    path: /models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf
  model_name: chat_instruct
  provider: litellm_proxy
- litellm_model_name: litellm_proxy/qwen2.5-1.5b-instruct-q4km-3
  litellm_params:
    api_base: http://gpustack-server:80/v1
    api_key: gpustack_643730d961b0a990_f2107549c86b1dc42fba04c1fc983ffa
    model: litellm_proxy/qwen2.5-1.5b-instruct-q4km-3
  model_info:
    id: instruct-endpoint-3
    path: /models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf
  model_name: chat_instruct
  provider: litellm_proxy
- litellm_model_name: litellm_proxy/qwen2.5-1.5b-instruct-q4km-1
  litellm_params:
    api_base: http://gpustack-server:80/v1
    api_key: gpustack_643730d961b0a990_f2107549c86b1dc42fba04c1fc983ffa
    model: litellm_proxy/qwen2.5-1.5b-instruct-q4km-1
  model_info:
    id: coder-endpoint-1
    path: /models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf
  model_name: chat_coder
  provider: litellm_proxy
- litellm_model_name: litellm_proxy/qwen2.5-1.5b-instruct-q4km-2
  litellm_params:
    api_base: http://gpustack-server:80/v1
    api_key: gpustack_643730d961b0a990_f2107549c86b1dc42fba04c1fc983ffa
    model: litellm_proxy/qwen2.5-1.5b-instruct-q4km-2
  model_info:
    id: coder-endpoint-2
    path: /models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf
  model_name: chat_coder
  provider: litellm_proxy
- litellm_model_name: litellm_proxy/qwen2.5-1.5b-instruct-q4km-3
  litellm_params:
    api_base: http://gpustack-server:80/v1
    api_key: gpustack_643730d961b0a990_f2107549c86b1dc42fba04c1fc983ffa
    model: litellm_proxy/qwen2.5-1.5b-instruct-q4km-3
  model_info:
    id: coder-endpoint-3
    path: /models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf
  model_name: chat_coder
  provider: litellm_proxy
