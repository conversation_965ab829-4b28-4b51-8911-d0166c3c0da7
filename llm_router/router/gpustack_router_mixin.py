#!/usr/bin/env python3
"""
Миксин роутера GPUStack.

Этот модуль предоставляет миксин класс для интеграции данных физических машин GPUStack
в процесс принятия решений LLM роутера. Обеспечивает улучшенную маршрутизацию
на основе метрик инфраструктуры в реальном времени и информации о нагрузке воркеров.
"""

from typing import Dict, List, Any, Optional
import asyncio
import os
import time
import traceback

from llm_router.logging_utils import get_logger
from llm_router.router_gpustack.gpustack_auth import GPUStackAuth
from llm_router.router_gpustack.gpustack_cache_manager import GPUStackCacheManager
from llm_router.router_gpustack.gpustack_integration import GPUStackIntegrationManager
from llm_router.router_gpustack.gpustack_integration import extract_hardware_info
from llm_router.router_gpustack.gpustack_physical_machines_cache import (
    GPUStackPhysicalMachinesCache,
)

logger = get_logger(__name__)


class GPUStackRouterMixin:
    """
    Миксин класс для интеграции данных физических машин GPUStack в решения роутера.

    Предоставляет методы для доступа к информации о нагрузке воркеров, статусу физических машин
    и данным об использовании ресурсов для улучшения решений маршрутизации на основе
    метрик инфраструктуры в реальном времени.

    Attributes:
        router: Экземпляр роутера с кэшем (наследуется от родительского класса)
        _gpustack_integration: Экземпляр менеджера интеграции GPUStack
        _gpustack_cache: Экземпляр кэша физических машин

    """

    # Объявление атрибута router для типизации
    router: Any

    def __init__(self, *args, **kwargs):
        """
        Инициализация миксина роутера GPUStack.

        Args:
            *args: Список аргументов переменной длины, передаваемый родительскому классу
            **kwargs: Произвольные keyword аргументы, передаваемые родительскому классу
        """
        super().__init__(*args, **kwargs)
        self._gpustack_integration: Optional[GPUStackIntegrationManager] = None
        self._gpustack_cache: Optional[GPUStackPhysicalMachinesCache] = None
        self._gpustack_cache_manager: Optional[GPUStackCacheManager] = None
        logger.info("GPUSTACK: инициализация миксина роутера GPUStack")

    def _init_gpustack(self) -> None:
        """
        Инициализация интеграции GPUStack с использованием констант.

        Создает экземпляры аутентификации, кэша и менеджера интеграции на основе
        констант из модуля constants и переменных среды.

        Все настройки берутся из констант: GPUSTACK_ENABLED, GPUSTACK_CACHE_TTL.

        Raises:
            ImportError: Если требуемые зависимости недоступны
            ValueError: Если значения конфигурации некорректны
        """
        from llm_router.constants import GPUSTACK_ENABLED, GPUSTACK_CACHE_TTL

        logger.debug("GPUSTACK: запуск инициализации интеграции GPUStack")

        if not GPUSTACK_ENABLED:
            logger.info(
                "GPUSTACK: интеграция отключена через константу GPUSTACK_ENABLED"
            )
            return

        try:
            logger.debug(
                "GPUSTACK: интеграция GPUStack включена, продолжаем с конфигурацией"
            )
            api_base = os.getenv("GPUSTACK_INTERNAL") or os.getenv("GPUSTACK_URL")
            api_key = os.getenv("GPUSTACK_KEY")
            cache_ttl = GPUSTACK_CACHE_TTL
            logger.debug(
                f"GPUSTACK: конфигурация - API base: {('установлен' if api_base else 'отсутствует')}, API key: {('установлен' if api_key else 'отсутствует')}, cache TTL: {cache_ttl}s"
            )
            if not api_base:
                logger.error(
                    "GPUSTACK: базовый URL API не найден в настройках роутера или переменных среды"
                )
                raise ValueError(
                    "GPUStack API base URL отсутствует - требуется GPUSTACK_INTERNAL или GPUSTACK_URL"
                )
            if not api_key:
                logger.error(
                    "GPUSTACK: API ключ не найден в настройках роутера или переменных среды"
                )
                raise ValueError(
                    "GPUStack API key отсутствует - требуется GPUSTACK_KEY"
                )
            if cache_ttl <= 0:
                logger.error("GPUSTACK: некорректное значение TTL кэша: %s", cache_ttl)
                raise ValueError(
                    f"cache_ttl должен быть положительным, получен {cache_ttl}"
                )
            logger.debug("GPUSTACK: создание экземпляра аутентификации")
            auth = GPUStackAuth(api_base=api_base, api_key=api_key, auth_mode="key")
            self._gpustack_cache = GPUStackPhysicalMachinesCache(
                cache_backend=self.router.cache, default_ttl=cache_ttl
            )
            logger.debug("GPUSTACK: кэш физических машин инициализирован")
            self._gpustack_integration = GPUStackIntegrationManager(
                auth=auth, cache=self._gpustack_cache
            )
            self._gpustack_cache_manager = GPUStackCacheManager(
                gpustack_integration=self._gpustack_integration,
                gpustack_cache=self._gpustack_cache,
                update_interval=30,
            )
            logger.info("GPUSTACK: интеграция GPUStack успешно инициализирована")
            logger.debug(
                f"GPUSTACK: детали интеграции - API base: {api_base}, cache TTL: {cache_ttl}s"
            )
        except ValueError as e:
            logger.error(f"GPUSTACK: некорректные значения конфигурации: {e}")
        except Exception as e:
            logger.error(
                "GPUSTACK: неожиданная ошибка при инициализации: %s", e, exc_info=True
            )
            raise RuntimeError("Инициализация интеграции GPUStack не удалась") from e

    async def _start_background_services(self) -> None:
        """
        Запуск background сервисов GPUStack (cache manager и health check).

        Выполняется асинхронно, чтобы избежать блокировки инициализации роутера.
        """
        try:
            logger.debug("GPUSTACK: запуск background сервисов")
            if self._gpustack_cache_manager:
                logger.debug("GPUSTACK: начинаем запуск cache manager")
                await self._gpustack_cache_manager.start()
                logger.info("GPUSTACK: background менеджер кэша запущен")
                logger.debug("GPUSTACK: ожидание первоначальной загрузки данных в кэш")
                data_ready = await self._gpustack_cache_manager.wait_for_initial_data(
                    timeout=30
                )
                if data_ready:
                    logger.info(
                        "GPUSTACK: первоначальные данные успешно загружены в кэш"
                    )
                    if self._gpustack_cache:
                        snapshot = await self._gpustack_cache.get_snapshot()
                        if snapshot:
                            logger.debug(
                                f"GPUSTACK: подтверждение данных в кэше - воркеры: {len(snapshot.workers)}, экземпляры: {len(snapshot.model_instances)}, timestamp: {snapshot.timestamp}, ttl: {snapshot.ttl_seconds}"
                            )
                        else:
                            logger.warning(
                                "GPUSTACK: кэш неожиданно пуст после успешного ожидания"
                            )
                else:
                    logger.warning(
                        "GPUSTACK: не удалось дождаться загрузки первоначальных данных в кэш"
                    )
            else:
                logger.warning("GPUSTACK: cache manager не инициализирован")
            await self._gpustack_health_check()
        except Exception as e:
            logger.error(
                f"GPUSTACK: ошибка запуска background сервисов: {e}", exc_info=True
            )

    async def _gpustack_health_check(self) -> None:
        """
        Выполнение начальной проверки работоспособности GPUStack в фоне.

        Выполняет проверку работоспособности асинхронно, чтобы избежать блокировки инициализации роутера.
        Результаты логируются, но не влияют на статус интеграции.
        """
        try:
            logger.debug("GPUSTACK: запуск фоновой проверки работоспособности")
            if not self._gpustack_integration:
                logger.warning(
                    "GPUSTACK: невозможно выполнить проверку работоспособности - менеджер интеграции недоступен"
                )
                return
            health_ok = await self._gpustack_integration.health_check()
            if health_ok:
                logger.info(
                    "GPUSTACK: начальная проверка работоспособности прошла успешно"
                )
            else:
                logger.warning(
                    "GPUSTACK: начальная проверка работоспособности не удалась - API может быть недоступен"
                )
        except Exception as e:
            logger.error(
                f"GPUSTACK: ошибка проверки работоспособности: {e}", exc_info=True
            )

    async def stop_background_services(self) -> None:
        """
        Остановка background сервисов GPUStack при shutdown роутера.

        Gracefully останавливает cache manager и другие background процессы.
        """
        try:
            logger.info("GPUSTACK: остановка background сервисов")
            if self._gpustack_cache_manager:
                await self._gpustack_cache_manager.stop()
                logger.info("GPUSTACK: background менеджер кэша остановлен")
        except Exception as e:
            logger.error(
                f"GPUSTACK: ошибка остановки background сервисов: {e}", exc_info=True
            )

    async def get_gpustack_worker_loads(
        self, deployment_ids: Optional[List[str]] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Получение информации о нагрузке воркеров из GPUStack для конкретных развертываний.

        Извлекает данные о нагрузке физических машин для указанных развертываний или всех
        доступных воркеров, если ID развертываний не предоставлены.

        Args:
            deployment_ids: Список ID развертываний для получения информации о воркерах, или None для всех воркеров

        Returns:
            Словарь, отображающий ID воркеров на их информацию о нагрузке, включая
            использование CPU, памяти, загрузку GPU и активные запросы.
            Возвращает пустой dict если интеграция GPUStack отключена или не работает.

        Raises:
            ConnectionError: Если невозможно подключиться к API GPUStack
            TimeoutError: Если истекло время ожидания API запроса
        """
        if not self._gpustack_integration:
            logger.debug(
                "GPUSTACK: интеграция GPUStack недоступна для получения нагрузки воркеров"
            )
            return {}
        logger.debug(
            f"GPUSTACK: получение нагрузки воркеров для развертываний: {deployment_ids or 'all'}"
        )
        try:
            worker_ids = None
            if deployment_ids:
                worker_ids = await self._map_deployments_to_workers(deployment_ids)
                logger.debug(
                    f"GPUSTACK: сопоставлено {len(deployment_ids)} развертываний с {(len(worker_ids) if worker_ids else 0)} воркерами"
                )
            worker_loads = await self._gpustack_integration.get_worker_load_data(
                worker_ids
            )
            if worker_loads:
                logger.debug(
                    f"GPUSTACK: успешно получены данные о нагрузке для {len(worker_loads)} воркеров"
                )
                return worker_loads
            else:
                logger.debug("GPUSTACK: данные о нагрузке воркеров недоступны")
                return {}
        except ConnectionError as e:
            logger.error(
                f"GPUSTACK: соединение не удалось при получении нагрузки воркеров: {e}"
            )
            return {}
        except TimeoutError as e:
            logger.error(f"GPUSTACK: таймаут при получении нагрузки воркеров: {e}")
            return {}
        except Exception as e:
            logger.error(
                f"GPUSTACK: неожиданная ошибка получения нагрузки воркеров: {e}",
                exc_info=True,
            )
            logger.debug(
                f"GPUSTACK: контекст ошибки нагрузки воркеров - deployment_ids: {deployment_ids}"
            )
            return {}

    async def get_gpustack_deployment_info(
        self, deployment_id: str, model_name: str
    ) -> Dict[str, Any]:
        """
        Получение информации о развертывании из кэшированных данных GPUStack.

        Получает детальную информацию о конкретном развертывании модели из кэшированного
        снимка физических машин GPUStack. Быстрая синхронная операция.

        Args:
            deployment_id: Идентификатор развертывания LiteLLM для поиска
            model_name: Имя модели для поиска в GPUStack (с автоматическим удалением префикса litellm_proxy/)

        Returns:
            Словарь с информацией о развертывании:
            - deployment_id: ID развертывания
            - model_name: Имя модели
            - worker_id: ID воркера исполняющего модель
            - worker_ip/gpu_info/status и т.д.
            Возвращает пустой dict если развертывание не найдено или кэш недоступен.

        Raises:
            ValueError: Если переданы некорректные параметры
        """
        if not deployment_id or not deployment_id.strip():
            logger.error(
                "GPUSTACK: параметр deployment_id обязателен и не может быть пустым"
            )
            raise ValueError("deployment_id не может быть пустым")
        if not model_name or not model_name.strip():
            logger.error(
                f"GPUSTACK: параметр model_name обязателен для развертывания {deployment_id}"
            )
            raise ValueError(
                f"model_name не может быть пустым для развертывания {deployment_id}"
            )
        if not self._gpustack_integration:
            logger.debug(
                f"GPUSTACK: интеграция недоступна для получения информации о развертывании {deployment_id}"
            )
            return {}
        try:
            snapshot = None
            if self._gpustack_cache:
                snapshot = await self._gpustack_cache.get_snapshot()
            if not snapshot:
                logger.debug(
                    f"GPUSTACK: кэшированный снимок недоступен для развертывания {deployment_id}"
                )
                return {}
            if model_name.startswith("litellm_proxy/"):
                logger.debug(
                    f"GPUSTACK: убран префикс litellm_proxy/ из '{model_name}'"
                )
                model_name = model_name[len("litellm_proxy/") :]
            logger.debug(f"GPUSTACK: поиск в снимке по имени модели: '{model_name}'")
            model_instance = next(
                (
                    instance
                    for instance in snapshot.model_instances.values()
                    if instance.model_name == model_name
                ),
                None,
            )
            if not model_instance:
                logger.warning(
                    f"GPUSTACK: экземпляр модели с именем '{model_name}' не найден среди {len(snapshot.model_instances)} доступных"
                )
                logger.debug(
                    f"GPUSTACK: доступные имена моделей: {[instance.model_name for instance in snapshot.model_instances.values()]}"
                )
                return {}
            logger.debug(
                f"GPUSTACK: найден экземпляр {model_instance.id} для модели {model_name}"
            )
            model_replicas = model_instance.replicas
            model_ready_replicas = model_instance.ready_replicas
            physical_model_path = model_instance.model_name
            if model_instance.local_path:
                local_path = model_instance.local_path
                physical_model_path = (
                    local_path[len("/models") :].lstrip("/")
                    if local_path.startswith("/models")
                    else local_path
                )
                logger.info(
                    f"GPUSTACK: использую local_path для {deployment_id}: {physical_model_path}"
                )
            elif model_instance.resolved_path:
                resolved_path = model_instance.resolved_path
                physical_model_path = (
                    resolved_path[len("/models") :].lstrip("/")
                    if resolved_path.startswith("/models")
                    else resolved_path
                )
                logger.info(
                    f"GPUSTACK: использую resolved_path для модели {model_name} развертывания {deployment_id}: {physical_model_path}"
                )
            else:
                logger.debug(
                    f"GPUSTACK: физические пути недоступны для модели {model_name} развертывания {deployment_id}, использую model_name"
                )
            worker_info = (
                snapshot.get_worker_by_id(model_instance.worker_id)
                if model_instance.worker_id
                else None
            )
            if not worker_info:
                logger.warning(f"GPUSTACK: воркер {model_instance.worker_id} не найден")
                return {}
            gpu_utilizations = [
                gpu.utilization
                for gpu in worker_info.gpus
                if gpu.utilization is not None
            ]
            avg_gpu_utilization = (
                sum(gpu_utilizations) / len(gpu_utilizations)
                if gpu_utilizations
                else 0.0
            )
            gpu_names = [gpu.name for gpu in worker_info.gpus if gpu.name]
            gpu_memory_total = 0
            if worker_info.gpus:
                for gpu in worker_info.gpus:
                    if gpu.memory_total:
                        gpu_memory_total += gpu.memory_total
            deployment_info = {
                "deployment_id": deployment_id,
                "model_instance_id": model_instance.id,
                "model_name": model_instance.model_name,
                "physical_model_path": physical_model_path,
                "status": model_instance.status.value,
                "state_message": model_instance.state_message,
                "replicas": model_replicas,
                "ready_replicas": model_ready_replicas,
                "worker_id": worker_info.id,
                "worker_name": worker_info.name,
                "worker_status": worker_info.status.value,
                "worker_hostname": worker_info.hostname,
                "worker_ip": worker_info.ip_address,
                "active_requests": model_instance.active_requests,
                "gpu_memory_allocated": model_instance.gpu_memory_allocated,
                "gpu_memory_total": gpu_memory_total,
                "memory_allocated": model_instance.memory_allocated,
                "gpu_indexes": model_instance.gpu_indexes,
                "source": model_instance.source,
                "backend": model_instance.backend_info,
                "average_response_time": model_instance.average_response_time,
                "worker_cpu_usage": worker_info.cpu_usage,
                "worker_memory_usage": (
                    worker_info.memory_used / worker_info.memory_total
                    if worker_info.memory_total and worker_info.memory_used
                    else 0.0
                ),
                "worker_memory_total": worker_info.memory_total,
                "worker_gpu_count": len(worker_info.gpus),
                "gpu_names": gpu_names,
                "gpu_count": len(worker_info.gpus),
                "worker_avg_gpu_utilization": avg_gpu_utilization,
                "last_seen": (
                    worker_info.last_seen.isoformat() if worker_info.last_seen else None
                ),
                "created_at": (
                    model_instance.created_at.isoformat()
                    if model_instance.created_at
                    else None
                ),
                "updated_at": (
                    model_instance.updated_at.isoformat()
                    if model_instance.updated_at
                    else None
                ),
            }
            logger.info(
                f"GPUSTACK: получена информация для модели {model_name} развертывания {deployment_id}: воркер {worker_info.name}, replicas={model_replicas}/{model_ready_replicas}, активных запросов: {model_instance.active_requests}"
            )
            return deployment_info
        except Exception as e:
            logger.error(
                f"GPUSTACK: ошибка получения кэшированной информации о модели {model_name} развертывания {deployment_id}: {e}",
                exc_info=True,
            )
            return {}

    async def enrich_deployments_with_gpustack_data(
        self, deployments: List[Dict[str, Any]]
    ) -> None:
        """
        Обогащение данных развертывания информацией физических машин GPUStack.

        Асинхронная версия которая использует кэшированные данные GPUStack для обогащения
        развертываний in-place. Изменяет переданный список развертываний напрямую.

        Args:
            deployments: Список словарей развертываний для обогащения (изменяется in-place)

        Raises:
            ValueError: Если список развертываний содержит некорректные записи
            ConnectionError: Если невозможно подключиться к API GPUStack
            TimeoutError: Если истекло время ожидания API запросов
        """
        if not isinstance(deployments, list):
            raise ValueError("deployments должен быть списком")
        if not self._gpustack_integration:
            logger.debug("GPUSTACK: интеграция GPUStack недоступна для обогащения")
            return
        try:
            logger.debug(
                "GPUSTACK: использование кэшированных данных для обогащения развертываний"
            )
            if not self._gpustack_cache:
                logger.warning("GPUSTACK: кэш недоступен, пропускаем обогащение")
                return
            snapshot = await self._gpustack_cache.get_snapshot()
            if not snapshot:
                logger.warning(
                    "GPUSTACK: кэшированные данные недоступны, пропускаем обогащение"
                )
                return
            logger.debug(
                f"GPUSTACK: начинается кэшированное обогащение {len(deployments)} развертываний"
            )
            for deployment in deployments:
                deployment_id = deployment.get("model_info", {}).get("id")
                if not deployment_id:
                    logger.error(
                        f"GPUSTACK: deployment_id не найден для развертывания {deployment}"
                    )
                    continue
                litellm_model = deployment.get("litellm_params", {}).get("model")
                if not litellm_model:
                    logger.error(
                        f"GPUSTACK: litellm_model не найден для развертывания {deployment_id}"
                    )
                    continue
                logger.debug(f"GPUSTACK: deployment: {deployment}")
                logger.debug(
                    f"GPUSTACK: кэшированное обогащение deployment_id='{deployment_id}', litellm_model='{litellm_model}'"
                )
                gpustack_info = await self.get_gpustack_deployment_info(
                    deployment_id, litellm_model
                )
                if gpustack_info:
                    self._enrich_deployment_with_gpustack_info(
                        deployment, gpustack_info
                    )
                    composite_score = self.calculate_gpustack_composite_score(
                        deployment
                    )
                    deployment["gpustack_composite_score"] = composite_score
                    logger.debug(
                        f"GPUSTACK: кэшированное обогащение завершено для {deployment_id} (оценка: {composite_score:.3f})"
                    )
                    logger.debug(f"GPUSTACK: gpustack_info: {gpustack_info}")
                else:
                    logger.debug(
                        f"GPUSTACK: кэшированные данные не найдены для развертывания {deployment_id} (модель: {litellm_model})"
                    )
            logger.debug(
                f"GPUSTACK: успешно обогащено {len(deployments)} развертываний"
            )
        except ConnectionError as e:
            logger.error(
                f"GPUSTACK: соединение не удалось при обогащении развертываний: {e}"
            )
        except TimeoutError as e:
            logger.error(f"GPUSTACK: таймаут при обогащении развертываний: {e}")
        except Exception as e:
            logger.error(
                f"GPUSTACK: неожиданная ошибка обогащения развертываний: {e}",
                exc_info=True,
            )
            logger.debug(
                f"GPUSTACK: контекст ошибки обогащения - количество развертываний: {len(deployments)}"
            )

    async def _map_deployments_to_workers(self, deployment_ids: List[str]) -> List[str]:
        """
        Сопоставление ID развертываний с ID воркеров, используя данные GPUStack.

        Находит соответствующие ID воркеров для заданных ID развертываний путем сопоставления
        с экземплярами моделей в снимке физических машин.

        Args:
            deployment_ids: Список ID развертываний для сопоставления (обязательно)

        Returns:
            Список уникальных ID воркеров, соответствующих развертываниям.
            Возвращает пустой список если интеграция отключена или совпадения не найдены.

        Raises:
            ValueError: Если deployment_ids не является корректным списком
        """
        if not isinstance(deployment_ids, list):
            logger.error("GPUSTACK: deployment_ids должен быть списком")
            raise ValueError("deployment_ids должен быть списком")
        if not self._gpustack_integration:
            logger.debug(
                "GPUSTACK: интеграция недоступна для сопоставления развертываний"
            )
            return []
        logger.debug(
            f"GPUSTACK: сопоставление {len(deployment_ids)} развертываний с воркерами"
        )
        try:
            snapshot = await self._gpustack_integration.get_physical_machines_data()
            if not snapshot:
                logger.warning(
                    "GPUSTACK: снимок физических машин недоступен для сопоставления"
                )
                return []
            worker_ids = []
            mapped_count = 0
            for deployment_id in deployment_ids:
                found_worker = False
                for instance in snapshot.model_instances.values():
                    if (
                        str(instance.id) == str(deployment_id)
                        or instance.model_name == deployment_id
                        or deployment_id in instance.model_name
                    ):
                        if instance.worker_id:
                            worker_ids.append(str(instance.worker_id))
                            mapped_count += 1
                            found_worker = True
                            logger.debug(
                                f"GPUSTACK: сопоставлено развертывание {deployment_id} с воркером {instance.worker_id}"
                            )
                        break
                if not found_worker:
                    logger.debug(
                        f"GPUSTACK: воркер не найден для развертывания {deployment_id}"
                    )
            unique_worker_ids = list(dict.fromkeys(worker_ids))
            logger.info(
                f"GPUSTACK: успешно сопоставлено {mapped_count}/{len(deployment_ids)} развертываний с {len(unique_worker_ids)} уникальными воркерами"
            )
            return unique_worker_ids
        except Exception as e:
            logger.error(
                f"GPUSTACK: неожиданная ошибка сопоставления развертываний с воркерами: {e}",
                exc_info=True,
            )
            logger.debug(
                f"GPUSTACK: контекст ошибки сопоставления - deployment_ids: {deployment_ids}"
            )
            return []

    def get_gpustack_enhanced_ranking_factors(
        self, deployment: Dict[str, Any]
    ) -> Dict[str, float]:
        """
        Получение улучшенных факторов ранжирования на основе данных физических машин GPUStack.

        Вычисляет индивидуальные факторы производительности из данных GPUStack для помощи
        в определении оптимального развертывания для решений маршрутизации.

        Args:
            deployment: Словарь развертывания, содержащий информацию GPUStack и
                       данные physical_machine_load

        Returns:
            Словарь с нормализованными факторами ранжирования (0.0-1.0) для выбора маршрута:
            - health_factor: Состояние здоровья воркера (готов=1.0, недоступен=0.0)
            - requests_factor: Нагрузка активных запросов (меньше запросов = выше оценка)
            - gpu_factor: Доступность GPU (меньше загрузка = выше оценка)
            - cpu_factor: Доступность CPU (меньше использование = выше оценка)
            - memory_factor: Доступность памяти (меньше использование = выше оценка)
            - response_factor: Производительность времени ответа (быстрее = выше оценка)
            Возвращает пустой dict если данные GPUStack недоступны.

        Raises:
            ValueError: Если параметр deployment не является корректным словарем
        """
        if not isinstance(deployment, dict):
            logger.error("GPUSTACK: параметр deployment должен быть словарем")
            raise ValueError("параметр deployment должен быть словарем")
        try:
            gpustack_info = deployment.get("gpustack_info", {})
            physical_load = deployment.get("physical_machine_load", {})
            deployment_id = deployment.get("model_info", {}).get("id", "unknown")
            logger.debug(
                f"GPUSTACK: вычисление факторов ранжирования для развертывания {deployment_id}"
            )
            logger.debug(
                f"GPUSTACK: источники deployment_id - gpustack_info: {gpustack_info.get('deployment_id')}, model_info: {deployment.get('model_info', {}).get('id')}, fallback: {deployment.get('deployment_id')}"
            )
            if not gpustack_info or not physical_load:
                logger.debug(
                    f"GPUSTACK: данные GPUStack недоступны для факторов ранжирования (развертывание {deployment_id})"
                )
                return {}
            worker_status = physical_load.get("worker_status", "unknown")
            health_factor = {
                "ready": 1.0,
                "not_ready": 0.3,
                "unreachable": 0.0,
                "unknown": 0.1,
            }.get(worker_status, 0.1)
            active_requests = physical_load.get("active_requests", 0)
            max_requests = 10
            requests_factor = max(0.0, 1.0 - active_requests / max_requests)
            gpu_utilization = physical_load.get("gpu_utilization", 0.0)
            gpu_factor = max(0.0, 1.0 - gpu_utilization)
            cpu_usage = physical_load.get("cpu_usage", 0.0)
            cpu_factor = max(0.0, 1.0 - cpu_usage)
            memory_usage = physical_load.get("memory_usage", 0.0)
            memory_factor = max(0.0, 1.0 - memory_usage)
            avg_response_time = gpustack_info.get("average_response_time")
            if avg_response_time and avg_response_time > 0:
                max_response_time = 5.0
                response_factor = max(0.0, 1.0 - avg_response_time / max_response_time)
            else:
                response_factor = 1.0
            ranking_factors = {
                "health_factor": health_factor,
                "requests_factor": requests_factor,
                "gpu_factor": gpu_factor,
                "cpu_factor": cpu_factor,
                "memory_factor": memory_factor,
                "response_factor": response_factor,
            }
            logger.debug(
                f"GPUSTACK: факторы ранжирования для {deployment_id} - статус: {worker_status}, запросы: {active_requests}, gpu: {gpu_utilization:.2f}, cpu: {cpu_usage:.2f}"
            )
            logger.debug(
                f"GPUSTACK: вычисленные факторы для {deployment_id}: здоровье={health_factor:.2f}, запросы={requests_factor:.2f}, gpu={gpu_factor:.2f}, cpu={cpu_factor:.2f}"
            )
            return ranking_factors
        except Exception as e:
            logger.error(
                f"GPUSTACK: неожиданная ошибка вычисления факторов ранжирования: {e}",
                exc_info=True,
            )
            return {}

    def calculate_gpustack_composite_score(self, deployment: Dict[str, Any]) -> float:
        """
        Вычисление композитной оценки для развертывания на основе данных GPUStack.

        Объединяет множественные метрики физических машин в единую взвешенную оценку
        для решений маршрутизации. Алгоритм оценки учитывает:
        - Статус здоровья воркера (30% веса)
        - Нагрузка активных запросов (25% веса)
        - Доступность GPU (20% веса)
        - Доступность CPU (10% веса)
        - Доступность памяти (10% веса)
        - Производительность времени ответа (5% веса)

        Args:
            deployment: Словарь развертывания, содержащий информацию GPUStack
                       и данные physical_machine_load

        Returns:
            Композитная оценка между 0.0 и 1.0 где:
            - 1.0 = оптимальное развертывание (здоровое, малая нагрузка, быстрый ответ)
            - 0.5 = нейтральное/неизвестное (данные недоступны)
            - 0.0 = плохое развертывание (нездоровое, перегруженное, медленное)

        Raises:
            ValueError: Если словарь развертывания некорректен или отсутствуют требуемые данные
        """
        if not isinstance(deployment, dict):
            logger.error("GPUSTACK: параметр deployment должен быть словарем")
            raise ValueError("параметр deployment должен быть словарем")
        try:
            ranking_factors = self.get_gpustack_enhanced_ranking_factors(deployment)
            if not ranking_factors:
                return 0.5
            weights = {
                "health_factor": 0.3,
                "requests_factor": 0.25,
                "gpu_factor": 0.2,
                "cpu_factor": 0.1,
                "memory_factor": 0.1,
                "response_factor": 0.05,
            }
            composite_score = 0.0
            total_weight = 0.0
            for factor_name, weight in weights.items():
                factor_value = ranking_factors.get(factor_name, 0.0)
                composite_score += factor_value * weight
                total_weight += weight
            if total_weight > 0:
                composite_score = composite_score / total_weight
            composite_score = max(0.0, min(1.0, composite_score))
            logger.debug(
                f"GPUSTACK: вычисление композитной оценки - здоровье: {ranking_factors.get('health_factor', 0):.2f}, запросы: {ranking_factors.get('requests_factor', 0):.2f}, gpu: {ranking_factors.get('gpu_factor', 0):.2f}, итого: {composite_score:.3f}"
            )
            return composite_score
        except (KeyError, TypeError) as e:
            logger.error(
                f"GPUSTACK: некорректные данные развертывания для вычисления композитной оценки: {e}"
            )
            logger.debug(
                f"GPUSTACK: контекст ошибки композитной оценки - ключи развертывания: {(list(deployment.keys()) if isinstance(deployment, dict) else 'не dict')}"
            )
            return 0.5
        except Exception as e:
            logger.error(
                f"GPUSTACK: неожиданная ошибка вычисления композитной оценки: {e}",
                exc_info=True,
            )
            return 0.5

    def get_cache_manager_status(self) -> Dict[str, Any]:
        """
        Получение статуса background менеджера кэша.

        Returns:
            Словарь с информацией о состоянии менеджера кэша
        """
        if self._gpustack_cache_manager:
            return self._gpustack_cache_manager.get_status()
        else:
            return {"error": "Cache manager не инициализирован"}

    async def force_cache_update(self) -> bool:
        """
        Принудительное обновление кэша через background менеджер.

        Returns:
            True если обновление прошло успешно, False иначе
        """
        if self._gpustack_cache_manager:
            return await self._gpustack_cache_manager.force_update()
        else:
            logger.warning(
                "GPUSTACK: cache manager не инициализирован для принудительного обновления"
            )
            return False

    async def get_gpustack_health_status(self) -> Dict[str, Any]:
        """
        Получение исчерпывающего статуса здоровья интеграции GPUStack.

        Выполняет проверки здоровья и сообщает текущий статус всех
        компонентов интеграции GPUStack, включая подключение к API,
        доступность кэша и свежесть данных.

        Returns:
            Словарь с исчерпывающей информацией о статусе здоровья:
            - enabled: Включена ли интеграция GPUStack
            - integration_available: Инициализирован ли менеджер интеграции
            - cache_available: Доступен ли бэкенд кэша
            - api_healthy: Отвечает ли API GPUStack
            - last_fetch_successful: Была ли последняя выборка данных успешной
            - workers_count: Количество воркеров в текущем снимке
            - instances_count: Количество экземпляров моделей в текущем снимке
            - error_message: Описание обнаруженных ошибок

        Raises:
            Exception: Перебрасывает любые критические ошибки, обнаруженные во время проверки здоровья
        """
        health_status = {
            "enabled": True,
            "integration_available": self._gpustack_integration is not None,
            "cache_available": self._gpustack_cache is not None,
            "api_healthy": False,
            "last_fetch_successful": False,
            "workers_count": 0,
            "instances_count": 0,
            "error_message": None,
        }
        if not self._gpustack_integration:
            health_status["error_message"] = "Интеграция GPUStack не инициализирована"
            return health_status
        try:
            if self._gpustack_cache_manager:
                cache_status = self._gpustack_cache_manager.get_status()
                health_status["api_healthy"] = cache_status.get(
                    "last_update_success", False
                )
                health_status["last_fetch_successful"] = cache_status.get(
                    "last_update_success", False
                )
                if self._gpustack_cache:
                    snapshot = await self._gpustack_cache.get_snapshot()
                    if snapshot:
                        health_status["workers_count"] = len(snapshot.workers)
                        health_status["instances_count"] = len(snapshot.model_instances)
                    else:
                        health_status["error_message"] = (
                            "Кэшированные данные недоступны"
                        )
            else:
                health_status["error_message"] = "Cache manager не инициализирован"
        except ConnectionError as e:
            health_status["error_message"] = f"Соединение не удалось: {e}"
            logger.error(
                f"GPUSTACK: соединение не удалось во время проверки здоровья: {e}"
            )
        except TimeoutError as e:
            health_status["error_message"] = f"Таймаут проверки здоровья: {e}"
            logger.error(f"GPUSTACK: таймаут во время проверки здоровья: {e}")
        except Exception as e:
            health_status["error_message"] = f"Неожиданная ошибка: {e}"
            logger.error(
                f"GPUSTACK: неожиданная ошибка получения статуса здоровья: {e}",
                exc_info=True,
            )
        logger.debug(
            f"GPUSTACK: проверка статуса здоровья завершена - включена: {health_status['enabled']}, api_healthy: {health_status['api_healthy']}, воркеры: {health_status['workers_count']}"
        )
        return health_status

    def _enrich_deployment_with_gpustack_info(
        self, deployment: Dict[str, Any], gpustack_info: Dict[str, Any]
    ) -> None:
        """
        Обогащение развертывания данными GPUStack.

        Общий метод для добавления всех необходимых блоков данных GPUStack
        к развертыванию. Модифицирует переданный объект deployment.

        Args:
            deployment: Словарь развертывания для обогащения (модифицируется)
            gpustack_info: Словарь с информацией GPUStack о развертывании
        """
        deployment["gpustack_info"] = gpustack_info
        hardware_info = extract_hardware_info(None, gpustack_info)
        gpu_count = hardware_info.get("gpu_count", 1)
        gpu_type = hardware_info.get("gpu_type")
        vram_gb = hardware_info.get("vram_gb")
        if gpu_type:
            if vram_gb:
                gpu_desc = f"{gpu_type}-{vram_gb}GB"
            else:
                gpu_desc = gpu_type
            hardware_desc = f"{gpu_count}x{gpu_desc}" if gpu_count > 1 else gpu_desc
        else:
            hardware_desc = f"{gpu_count}xGPU" if gpu_count > 0 else "CPU"
        deployment["gpustack_params"] = {
            "hardware": hardware_desc,
            "instances": gpustack_info.get(
                "ready_replicas", gpustack_info.get("replicas", 1)
            ),
            "model_name": gpustack_info.get(
                "physical_model_path", gpustack_info.get("model_name")
            ),
            "active_requests": gpustack_info.get("active_requests", 0),
        }
        if hardware_info:
            deployment["hardware_info"] = hardware_info
        deployment["physical_machine_load"] = {
            "cpu_usage": gpustack_info.get("worker_cpu_usage", 0.0),
            "memory_usage": gpustack_info.get("worker_memory_usage", 0.0),
            "gpu_utilization": gpustack_info.get("worker_avg_gpu_utilization", 0.0),
            "active_requests": gpustack_info.get("active_requests", 0),
            "worker_status": gpustack_info.get("worker_status", "unknown"),
            "last_seen": gpustack_info.get("last_seen"),
        }
