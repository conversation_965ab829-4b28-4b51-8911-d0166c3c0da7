#!/usr/bin/env python3
"""
Пример использования метрик в LLM Router.

Этот файл демонстрирует различные способы настройки и использования
метрик в проекте LLM Router.
"""

import os
import time
import requests
from llm_router.config.metrics_config import (
    setup_llm_router_metrics,
    enable_metrics_programmatically,
    verify_metrics_setup,
    get_metrics_status,
)


def example_basic_setup():
    """Пример базовой настройки метрик"""
    print("Пример: Базовая настройка метрик")

    # Метрики настраиваются автоматически при импорте llm_router
    # или при вызове apply_patch() в litellm_patch.py
    result = setup_llm_router_metrics()

    print("Результат настройки:")
    for key, value in result.items():
        print(f"  {key}: {value}")

    return result


def example_programmatic_setup():
    """Пример программной настройки метрик"""
    print("\nПример: Программная настройка метрик")

    # Включаем метрики программно
    result = enable_metrics_programmatically(
        prometheus=True,
        system_metrics=True,
        end_user_tracking=False,  # Отключено для производительности
        debug=False,
    )

    print("Результат программной настройки:")
    for key, value in result.items():
        print(f"  {key}: {value}")

    return result


def example_env_based_setup():
    """Пример настройки через переменные окружения"""
    print("\nПример: Настройка через переменные окружения")

    # Устанавливаем переменные окружения
    os.environ["LLM_ROUTER_PROMETHEUS_METRICS"] = "true"
    os.environ["LLM_ROUTER_SYSTEM_METRICS"] = "true"
    os.environ["LLM_ROUTER_END_USER_TRACKING"] = "false"
    os.environ["LLM_ROUTER_METRICS_DEBUG"] = "false"

    # Настраиваем метрики с учетом переменных окружения
    result = setup_llm_router_metrics()

    print("Переменные окружения:")
    for key in [
        "LLM_ROUTER_PROMETHEUS_METRICS",
        "LLM_ROUTER_SYSTEM_METRICS",
        "LLM_ROUTER_END_USER_TRACKING",
        "LLM_ROUTER_METRICS_DEBUG",
    ]:
        print(f"  {key}: {os.environ.get(key, 'не установлено')}")

    print("Результат настройки:")
    for key, value in result.items():
        print(f"  {key}: {value}")

    return result


def example_verify_metrics():
    """Пример проверки настройки метрик"""
    print("\nПример: Проверка настройки метрик")

    # Проверяем настройку
    is_setup = verify_metrics_setup()
    print(f"Метрики настроены: {'OK' if is_setup else 'FAIL'}")

    # Получаем статус
    status = get_metrics_status()
    print("Статус метрик:")
    for key, value in status.items():
        print(f"  {key}: {value}")

    return is_setup, status


def example_check_metrics_endpoint():
    """Пример проверки эндпоинта метрик"""
    print("\nПример: Проверка эндпоинта метрик")

    try:
        # Пытаемся получить метрики
        base_url = os.getenv("LITELLM_API_BASE", "http://localhost:4000").rstrip("/")
        metrics_url = f"{base_url}/metrics"
        response = requests.get(metrics_url, timeout=5)

        if response.status_code == 200:
            print("Эндпоинт /metrics доступен")
            print(f"Размер ответа: {len(response.text)} символов")

            # Показываем первые несколько строк метрик
            lines = response.text.split("\n")[:10]
            print("Первые строки метрик:")
            for line in lines:
                if line.strip():
                    print(f"  {line}")

            return True
        else:
            print(f"Эндпоинт /metrics недоступен (код: {response.status_code})")
            return False

    except requests.exceptions.RequestException as e:
        print(f"Ошибка подключения к /metrics: {e}")
        print("Убедитесь, что LiteLLM прокси сервер запущен")
        return False


def example_integration_in_code():
    """Пример интеграции метрик в код"""
    print("\nПример: Интеграция метрик в код")

    # Вот как можно интегрировать метрики в ваш код:
    print(
        """
# В вашем коде (например, в main функции):

from llm_router.config.metrics_config import setup_llm_router_metrics

def main():
    # Настройка метрик в начале программы
    metrics_config = setup_llm_router_metrics()
    
    if metrics_config.get("prometheus_metrics"):
        print("Метрики включены:", metrics_config["metrics_endpoint"])
    
    # Ваш основной код здесь
    # ...
    
    # Проверка метрик (опционально)
    from llm_router.config.metrics_config import verify_metrics_setup
    if verify_metrics_setup():
        print("Метрики работают корректно")

if __name__ == "__main__":
    main()
"""
    )


def run_all_examples():
    """Запуск всех примеров"""
    print("Примеры настройки метрик LLM Router")
    print("=" * 50)

    # Основные примеры
    example_basic_setup()
    example_programmatic_setup()
    example_env_based_setup()
    example_verify_metrics()

    # Проверка эндпоинта (может не работать если сервер не запущен)
    example_check_metrics_endpoint()

    # Пример интеграции
    example_integration_in_code()

    print("\n" + "=" * 50)
    print("Все примеры выполнены")
    print("Подробная документация: llm_router/docs/METRICS_CONFIGURATION_GUIDE.md")


if __name__ == "__main__":
    run_all_examples()
