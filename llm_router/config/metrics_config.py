#!/usr/bin/env python3
"""
Модуль настройки метрик для LLM Router.

Этот модуль обеспечивает программную настройку метрик LiteLLM
для интеграции с системой мониторинга LLM Router.
"""

import os
import litellm
from typing import Dict, Any, Optional

from llm_router.logging_utils import get_logger

logger = get_logger(__name__)


def get_metrics_url(prometheus_enabled: Optional[bool] = None) -> Optional[str]:
    """Общая функция для получения URL метрик

    Args:
        prometheus_enabled: Если передан, проверяет это значение.
                           Если None, проверяет состояние litellm callbacks
    """
    if prometheus_enabled is not None:
        # Используется из MetricsConfig
        if not prometheus_enabled:
            return None
    else:
        # Используется из standalone функций
        if "prometheus" not in getattr(litellm, "callbacks", []):
            return None

    base_url = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    # Убираем trailing slash если есть
    base_url = base_url.rstrip("/")
    return f"{base_url}/metrics"


class MetricsConfig:
    """Конфигурация метрик для LLM Router"""

    def __init__(self):
        self.prometheus_enabled = True  # По умолчанию включено
        self.system_metrics_enabled = True  # Системные метрики включены
        self.end_user_tracking_enabled = (
            False  # По умолчанию отключено для производительности
        )
        self.debug_mode = False

    def load_from_env(self, prefix: str = "LLM_ROUTER_") -> None:
        """Загрузка настроек из переменных окружения

        Args:
            prefix: Префикс для переменных окружения (например, 'LLM_ROUTER_', 'METRICS_', '')
        """
        self.prometheus_enabled = (
            os.getenv(f"{prefix}PROMETHEUS_METRICS", "true").lower() == "true"
        )
        self.system_metrics_enabled = (
            os.getenv(f"{prefix}SYSTEM_METRICS", "true").lower() == "true"
        )
        self.end_user_tracking_enabled = (
            os.getenv(f"{prefix}END_USER_TRACKING", "false").lower() == "true"
        )
        self.debug_mode = os.getenv(f"{prefix}METRICS_DEBUG", "false").lower() == "true"

        logger.info(f"METRICS: Загружены настройки из окружения:")
        logger.info(f"  prometheus_enabled: {self.prometheus_enabled}")
        logger.info(f"  system_metrics_enabled: {self.system_metrics_enabled}")
        logger.info(f"  end_user_tracking_enabled: {self.end_user_tracking_enabled}")
        logger.info(f"  debug_mode: {self.debug_mode}")

    def apply_to_litellm(self) -> Dict[str, Any]:
        """Применение настроек к LiteLLM"""
        # Получаем существующие callbacks, чтобы не затереть их
        existing_callbacks = getattr(litellm, "callbacks", []) or []
        existing_service_callbacks = getattr(litellm, "service_callback", []) or []

        # Создаем копии списков для безопасного изменения
        callbacks = list(existing_callbacks)
        service_callbacks = list(existing_service_callbacks)

        if self.prometheus_enabled:
            if "prometheus" not in callbacks:
                callbacks.append("prometheus")
                logger.info("METRICS: Prometheus callbacks добавлены")
            else:
                logger.info("METRICS: Prometheus callbacks уже настроены")

        if self.system_metrics_enabled:
            if "prometheus_system" not in service_callbacks:
                service_callbacks.append("prometheus_system")
                logger.info("METRICS: System metrics callbacks добавлены")
            else:
                logger.info("METRICS: System metrics callbacks уже настроены")

        # Применяем настройки к LiteLLM только если есть изменения
        if callbacks != existing_callbacks:
            litellm.callbacks = callbacks
            logger.info(f"METRICS: Обновлены callbacks: {callbacks}")

        if service_callbacks != existing_service_callbacks:
            litellm.service_callback = service_callbacks
            logger.info(f"METRICS: Обновлены service_callbacks: {service_callbacks}")

        # Настройка отслеживания пользователей
        setattr(
            litellm,
            "enable_end_user_cost_tracking_prometheus_only",
            self.end_user_tracking_enabled,
        )
        logger.info(f"METRICS: End user tracking: {self.end_user_tracking_enabled}")

        # Debug режим
        if self.debug_mode:
            setattr(litellm, "set_verbose", True)
            logger.info("METRICS: Debug режим включен")

        result = {
            "prometheus_metrics": self.prometheus_enabled,
            "system_metrics": self.system_metrics_enabled,
            "end_user_tracking": self.end_user_tracking_enabled,
            "debug_mode": self.debug_mode,
            "metrics_endpoint": get_metrics_url(self.prometheus_enabled),
            "callbacks": callbacks,
            "service_callbacks": service_callbacks,
        }

        return result


def setup_llm_router_metrics(env_prefix: str = "LLM_ROUTER_") -> Dict[str, Any]:
    """
    Настройка метрик для LLM Router.

    Эта функция вызывается из litellm_patch.py при применении патчей
    для автоматической настройки метрик.

    Args:
        env_prefix: Префикс для переменных окружения (по умолчанию 'LLM_ROUTER_')
                   Можно использовать 'METRICS_', '', и т.д.

    Returns:
        Dict[str, Any]: Словарь с информацией о настроенных метриках
    """
    logger.info(f"METRICS: Начало настройки метрик LLM Router (префикс: {env_prefix})")

    try:
        # Создаем конфигурацию
        config = MetricsConfig()

        # Загружаем настройки из окружения
        config.load_from_env(env_prefix)

        # Применяем к LiteLLM
        result = config.apply_to_litellm()

        logger.info("METRICS: Метрики LLM Router успешно настроены")

        return result

    except Exception as e:
        logger.error(f"METRICS: Ошибка настройки метрик: {e}")
        # Не прерываем выполнение, просто логируем ошибку
        return {
            "error": str(e),
            "prometheus_metrics": False,
            "system_metrics": False,
            "metrics_endpoint": None,
        }


def verify_metrics_setup() -> bool:
    """
    Проверка корректности настройки метрик.

    Returns:
        bool: True если метрики настроены корректно
    """
    logger.info("METRICS: Проверка настройки метрик...")

    try:
        # Проверяем настройки LiteLLM
        has_prometheus = "prometheus" in getattr(litellm, "callbacks", [])
        has_system = "prometheus_system" in getattr(litellm, "service_callback", [])
        end_user_setting = getattr(
            litellm, "enable_end_user_cost_tracking_prometheus_only", None
        )

        logger.info(
            f"METRICS: Prometheus callbacks: {'OK' if has_prometheus else 'DISABLED'}"
        )
        logger.info(f"METRICS: System callbacks: {'OK' if has_system else 'DISABLED'}")
        logger.info(f"METRICS: End user tracking: {end_user_setting}")

        # Считаем успешным если хотя бы один тип метрик включен
        success = has_prometheus or has_system

        if success:
            logger.info("METRICS: Проверка метрик прошла успешно")
            if has_prometheus:
                metrics_url = get_metrics_url()
                if metrics_url:
                    logger.info(f"METRICS: Метрики доступны по адресу: {metrics_url}")
        else:
            logger.warning("METRICS: Метрики не настроены")

        return success

    except Exception as e:
        logger.error(f"METRICS: Ошибка проверки метрик: {e}")
        return False


def get_metrics_status() -> Dict[str, Any]:
    """
    Получение текущего статуса метрик.

    Returns:
        Dict[str, Any]: Словарь с информацией о статусе метрик
    """
    try:
        status = {
            "prometheus_callbacks": getattr(litellm, "callbacks", []),
            "service_callbacks": getattr(litellm, "service_callback", []),
            "end_user_tracking": getattr(
                litellm, "enable_end_user_cost_tracking_prometheus_only", None
            ),
            "verbose_mode": getattr(litellm, "set_verbose", False),
            "has_prometheus": "prometheus" in getattr(litellm, "callbacks", []),
            "has_system_metrics": "prometheus_system"
            in getattr(litellm, "service_callback", []),
            "metrics_endpoint": get_metrics_url(),
        }

        return status

    except Exception as e:
        logger.error(f"METRICS: Ошибка получения статуса: {e}")
        return {"error": str(e)}


def enable_metrics_programmatically(
    prometheus: bool = True,
    system_metrics: bool = True,
    end_user_tracking: bool = False,
    debug: bool = False,
    env_prefix: str = "",
) -> Dict[str, Any]:
    """
    Программное включение метрик без использования переменных окружения.

    Args:
        prometheus: Включить Prometheus метрики
        system_metrics: Включить системные метрики
        end_user_tracking: Включить отслеживание пользователей
        debug: Включить debug режим
        env_prefix: Префикс для переменных окружения (если используется)

    Returns:
        Dict[str, Any]: Информация о настроенных метриках
    """
    logger.info("METRICS: Программное включение метрик")

    try:
        # Получаем существующие callbacks, чтобы не затереть их
        existing_callbacks = getattr(litellm, "callbacks", []) or []
        existing_service_callbacks = getattr(litellm, "service_callback", []) or []

        # Создаем копии списков для безопасного изменения
        callbacks = list(existing_callbacks)
        service_callbacks = list(existing_service_callbacks)

        if prometheus:
            if "prometheus" not in callbacks:
                callbacks.append("prometheus")
                logger.info("METRICS: Prometheus включен программно")
            else:
                logger.info("METRICS: Prometheus уже включен")

        if system_metrics:
            if "prometheus_system" not in service_callbacks:
                service_callbacks.append("prometheus_system")
                logger.info("METRICS: System metrics включены программно")
            else:
                logger.info("METRICS: System metrics уже включены")

        # Применяем к LiteLLM только если есть изменения
        if callbacks != existing_callbacks:
            litellm.callbacks = callbacks

        if service_callbacks != existing_service_callbacks:
            litellm.service_callback = service_callbacks

        setattr(
            litellm, "enable_end_user_cost_tracking_prometheus_only", end_user_tracking
        )

        if debug:
            setattr(litellm, "set_verbose", True)
            logger.info("METRICS: Debug режим включен")

        result = {
            "prometheus_metrics": prometheus,
            "system_metrics": system_metrics,
            "end_user_tracking": end_user_tracking,
            "debug_mode": debug,
            "metrics_endpoint": get_metrics_url(prometheus),
            "callbacks": callbacks,
            "service_callbacks": service_callbacks,
            "setup_method": "programmatic",
        }

        logger.info("METRICS: Программная настройка метрик завершена")
        return result

    except Exception as e:
        logger.error(f"METRICS: Ошибка программной настройки: {e}")
        return {"error": str(e)}


def get_existing_callbacks() -> Dict[str, Any]:
    """
    Получить информацию о существующих callbacks в LiteLLM.

    Returns:
        Dict[str, Any]: Информация о существующих callbacks
    """
    existing_callbacks = getattr(litellm, "callbacks", []) or []
    existing_service_callbacks = getattr(litellm, "service_callback", []) or []

    return {
        "callbacks": existing_callbacks,
        "service_callbacks": existing_service_callbacks,
        "has_prometheus": "prometheus" in existing_callbacks,
        "has_prometheus_system": "prometheus_system" in existing_service_callbacks,
        "total_callbacks": len(existing_callbacks),
        "total_service_callbacks": len(existing_service_callbacks),
    }


def remove_metrics_callbacks() -> Dict[str, Any]:
    """
    Удалить только callbacks метрик, оставив остальные.

    Returns:
        Dict[str, Any]: Результат операции
    """
    logger.info("METRICS: Удаление callbacks метрик")

    try:
        existing_callbacks = getattr(litellm, "callbacks", []) or []
        existing_service_callbacks = getattr(litellm, "service_callback", []) or []

        # Удаляем только метрики callbacks
        new_callbacks = [cb for cb in existing_callbacks if cb != "prometheus"]
        new_service_callbacks = [
            cb for cb in existing_service_callbacks if cb != "prometheus_system"
        ]

        # Обновляем только если есть изменения
        if new_callbacks != existing_callbacks:
            litellm.callbacks = new_callbacks
            logger.info(f"METRICS: Callbacks обновлены: {new_callbacks}")

        if new_service_callbacks != existing_service_callbacks:
            litellm.service_callback = new_service_callbacks
            logger.info(
                f"METRICS: Service callbacks обновлены: {new_service_callbacks}"
            )

        return {
            "removed": True,
            "old_callbacks": existing_callbacks,
            "new_callbacks": new_callbacks,
            "old_service_callbacks": existing_service_callbacks,
            "new_service_callbacks": new_service_callbacks,
        }

    except Exception as e:
        logger.error(f"METRICS: Ошибка удаления callbacks: {e}")
        return {"error": str(e)}


# Вспомогательные функции для разных префиксов
def setup_with_short_prefix() -> Dict[str, Any]:
    """Настройка с коротким префиксом METRICS_"""
    return setup_llm_router_metrics("METRICS_")


def setup_without_prefix() -> Dict[str, Any]:
    """Настройка без префикса"""
    return setup_llm_router_metrics("")


def setup_with_custom_prefix(prefix: str) -> Dict[str, Any]:
    """Настройка с пользовательским префиксом"""
    return setup_llm_router_metrics(prefix)
