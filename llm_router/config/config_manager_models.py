#!/usr/bin/env python3
"""
LiteLLM Model Configuration Manager.

This script provides utilities for managing LiteLLM models through the /model/new API
instead of the configuration API. It properly handles model creation, listing, and updates.

Commands:
- list: Show current models from API
- add-models: Add models from YAML configuration to database via /model/new API
- clear-models: Delete all models from database
- update-models: Update all models from YAML configuration

This manager ensures models are properly stored in the database and loaded by the router.
"""
from pathlib import Path
from typing import Dict, Any, Optional, List
import argparse
import json
import os
import sys
import traceback

from host_common import get_logger
from host_common.config import get_env_variable

# Прямой импорт модулей, минуя пакет llm_router
import sys
from pathlib import Path

# Добавляем путь для прямого импорта модулей
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))

# Импортируем напрямую, минуя llm_router.__init__.py
from router_litellm.litellm_config_api import load_yaml_config
from router_litellm.litellm_manager import LiteLLMManager, ModelConfig

logger = get_logger(__name__)
DEFAULT_LITELLM_URL = get_env_variable("LITELLM_API_BASE") or "http://localhost:4000"


class ModelConfigManager:
    """
    Manager for LiteLLM model configurations using /model/new API.

    This class provides comprehensive model management functionality including
    creation, listing, deletion, and updates through the LiteLLM API. It handles
    proper configuration transformation and provides detailed error reporting.

    Attributes:
        litellm_url: Base URL for LiteLLM API
        api_key: API key for authentication
        manager: LiteLLMManager instance for API operations
    """

    def __init__(self, litellm_url: str, api_key: Optional[str] = None):
        """
        Initialize the model config manager.

        Args:
            litellm_url: Base URL for LiteLLM API
            api_key: API key for authentication (optional)
        """
        self.litellm_url = litellm_url
        self.api_key = api_key
        self.manager = LiteLLMManager(api_base=litellm_url, api_key=api_key)
        logger.info(f"INIT: Model config manager initialized with URL: {litellm_url}")
        if api_key:
            logger.info("INIT: Using API key authentication")
        else:
            logger.warning("INIT: No API key provided")

    def list_models(self) -> List[Dict[str, Any]]:
        """
        List all models from LiteLLM database.

        Returns:
            List of model configurations from the database

        Raises:
            Exception: If API request fails
        """
        logger.info("MODELS: Retrieving model list from API")
        try:
            models = self.manager.get_model_info()
            logger.info(f"MODELS: Found {len(models)} models")
            return models
        except Exception as e:
            logger.error(f"MODELS: Error retrieving models: {e}")
            return []

    def add_models_from_config(self, config_file: str) -> bool:
        """
        Add models from YAML configuration file using /model/new API.

        Processes each model in the configuration file and attempts to create it
        via the LiteLLM API. Provides detailed error reporting for failed models.

        Args:
            config_file: Path to YAML configuration file

        Returns:
            True if all models were added successfully, False otherwise

        Raises:
            Exception: If configuration file cannot be loaded
        """
        logger.info(f"MODELS: Loading configuration from {config_file}")
        try:
            config = load_yaml_config(config_file)
            if not config:
                logger.error("MODELS: Failed to load configuration")
                return False
            model_list = config.get("model_list", [])
            if not model_list:
                logger.warning("MODELS: No models found in configuration")
                return True
            logger.info(f"MODELS: Found {len(model_list)} models in configuration")
            success_count = 0
            error_count = 0
            failed_models = []
            for i, model_config in enumerate(model_list, 1):
                model_name = model_config.get("model_name", f"model_{i}")
                logger.info(f"MODELS: Adding model {i}/{len(model_list)}: {model_name}")
                try:
                    api_model_config = self._transform_model_config(model_config)
                    result = self.manager.create_model(api_model_config)
                    if result:
                        logger.info(f"MODELS: Successfully added model: {model_name}")
                        logger.debug(f"MODELS: Model creation result: {result}")
                        success_count += 1
                    else:
                        logger.error(f"MODELS: Failed to add model: {model_name}")
                        failed_models.append(f"{model_name} (creation failed)")
                        error_count += 1
                except Exception as e:
                    logger.error(f"MODELS: Error adding model {model_name}: {e}")
                    logger.debug(f"MODELS: Model config: {model_config}")
                    failed_models.append(f"{model_name} ({str(e)})")
                    error_count += 1
            if failed_models:
                logger.error(f"MODELS: Failed models: {failed_models}")
            logger.info(
                f"MODELS: Model addition completed - Success: {success_count}, Errors: {error_count}"
            )
            return error_count == 0
        except Exception as e:
            logger.error(f"MODELS: Failed to process configuration: {e}")
            logger.debug(f"MODELS: Traceback: {traceback.format_exc()}")
            return False

    def _transform_model_config(self, model_config: Dict[str, Any]) -> ModelConfig:
        """
        Transform YAML model config to API format.

        Converts configuration from YAML format to the structure expected by
        the LiteLLM /model/new API endpoint. Ensures all LiteLLM parameters
        are preserved in litellm_params section.

        Args:
            model_config: Model configuration from YAML

        Returns:
            Transformed model configuration for API
        """
        # Comprehensive list of known LiteLLM parameters
        # Основные параметры для всех провайдеров
        known_litellm_params = {
            # Core parameters
            "model",
            "api_base",
            "api_key",
            "api_version",
            "api_type",
            "provider",
            "custom_llm_provider",
            "vertex_project",
            "vertex_location",
            # Request parameters
            "timeout",
            "stream",
            "max_tokens",
            "temperature",
            "top_p",
            "top_k",
            "frequency_penalty",
            "presence_penalty",
            "stop",
            "n",
            "logit_bias",
            "user",
            "response_format",
            "seed",
            "tools",
            "tool_choice",
            # Advanced parameters
            "drop_params",
            "headers",
            "extra_headers",
            "extra_query",
            "extra_body",
            "rpm",
            "tpm",
            "max_retries",
            "retry_delay",
            "base_model",
            # Authentication parameters
            "aws_access_key_id",
            "aws_secret_access_key",
            "aws_session_token",
            "aws_region",
            "azure_ad_token",
            "azure_client_id",
            "azure_client_secret",
            "azure_tenant_id",
            "google_application_credentials",
            "vertex_credentials",
            # Provider-specific parameters
            "huggingface_api_key",
            "anthropic_api_key",
            "cohere_api_key",
            "openai_api_key",
            "replicate_api_key",
            "together_api_key",
            "palm_api_key",
            "gemini_api_key",
            "bedrock_region",
            "cloudflare_api_key",
            "fireworks_api_key",
            # Proxy and routing parameters
            "litellm_params",
            "model_name",
            "model_id",
            "deployment_id",
            "fallbacks",
            "context_window_fallbacks",
            "allowed_fails",
            "cooldown_time",
            # Custom parameters for router
            "predictor_task_type",
            "predictor_params",
            "managed_model_info",
        }

        api_config: ModelConfig = {
            "model_name": model_config["model_name"],
            "litellm_params": {"model": model_config["model_name"]},
        }

        # Сначала копируем все из litellm_params если он существует
        if "litellm_params" in model_config:
            api_config["litellm_params"].update(model_config["litellm_params"])

        # Сохраняем model_info если он существует
        if "model_info" in model_config:
            api_config["model_info"] = model_config["model_info"]

        # Обрабатываем остальные поля верхнего уровня
        for key, value in model_config.items():
            if key not in ["model_name", "litellm_params", "model_info"]:
                # Если это известный параметр LiteLLM, добавляем в litellm_params
                if key in known_litellm_params:
                    api_config["litellm_params"][key] = value
                    logger.debug(
                        f"MODELS: Added LiteLLM parameter {key} to litellm_params"
                    )
                else:
                    # Неизвестные параметры добавляем в model_info
                    if "model_info" not in api_config:
                        api_config["model_info"] = {}
                    api_config["model_info"][key] = value
                    logger.debug(f"MODELS: Added unknown parameter {key} to model_info")

        logger.debug(f"MODELS: Transformed config for {api_config['model_name']}")
        logger.debug(
            f"MODELS: litellm_params keys: {list(api_config['litellm_params'].keys())}"
        )
        return api_config

    def clear_all_models(self) -> bool:
        """
        Delete all models from LiteLLM.

        Retrieves all models and attempts to delete each one individually.
        Provides detailed error reporting for failed deletions.

        Returns:
            True if all models were deleted successfully, False otherwise
        """
        logger.info("MODELS: Clearing all models from database")
        models = self.list_models()
        if not models:
            logger.info("MODELS: No models to delete")
            return True
        success_count = 0
        error_count = 0
        failed_models = []
        for model in models:
            model_id = (
                model.get("model_id")
                or model.get("id")
                or (
                    model.get("model_info", {}).get("id")
                    if isinstance(model.get("model_info"), dict)
                    else None
                )
            )
            model_name = model.get("model_name", "unknown")
            if not model_id:
                logger.warning(f"MODELS: Skipping model {model_name} - no ID found")
                logger.debug(
                    f"MODELS: Model data keys: {list(model.keys())} - model_info: {model.get('model_info', 'N/A')}"
                )
                failed_models.append(f"{model_name} (no ID found)")
                error_count += 1
                continue
            try:
                logger.info(f"MODELS: Deleting model: {model_name} (ID: {model_id})")
                result = self.manager.delete_model(model_id)
                if result:
                    logger.info(f"MODELS: Successfully deleted model: {model_name}")
                    success_count += 1
                else:
                    logger.error(f"MODELS: Failed to delete model: {model_name}")
                    failed_models.append(f"{model_name} (deletion failed)")
                    error_count += 1
            except Exception as e:
                logger.error(f"MODELS: Error deleting model {model_name}: {e}")
                failed_models.append(f"{model_name} ({str(e)})")
                error_count += 1
        if failed_models:
            logger.error(f"MODELS: Failed to delete models: {failed_models}")
        logger.info(
            f"MODELS: Model deletion completed - Success: {success_count}, Errors: {error_count}"
        )
        return error_count == 0

    def update_models_from_config(self, config_file: str) -> bool:
        """
        Update models by clearing existing and adding from config.

        Performs a complete model replacement by first deleting all existing
        models and then adding all models from the configuration file.

        Args:
            config_file: Path to YAML configuration file

        Returns:
            True if update was successful, False otherwise
        """
        logger.info("MODELS: Starting model update process")
        logger.info("MODELS: Step 1 - Clearing existing models")
        if not self.clear_all_models():
            logger.error("MODELS: Failed to clear existing models")
            return False
        logger.info("MODELS: Step 2 - Adding models from configuration")
        if not self.add_models_from_config(config_file):
            logger.error("MODELS: Failed to add models from configuration")
            return False
        logger.info("MODELS: Model update process completed successfully")
        return True


def create_model_config_manager(
    litellm_url: Optional[str] = None, api_key: Optional[str] = None
) -> ModelConfigManager:
    """
    Create a model config manager with auto-detected settings.

    Automatically detects LiteLLM URL and API key from environment variables
    if not explicitly provided.

    Args:
        litellm_url: LiteLLM API URL (auto-detected if not provided)
        api_key: API key (auto-detected from LITELLM_MASTER_KEY if not provided)

    Returns:
        Configured ModelConfigManager instance
    """
    if not litellm_url:
        litellm_url = DEFAULT_LITELLM_URL
        logger.info(f"CONFIG: Auto-detected LiteLLM URL: {litellm_url}")
    if not api_key:
        api_key = os.getenv("LITELLM_MASTER_KEY")
        if api_key:
            logger.info("CONFIG: Using LITELLM_MASTER_KEY from environment")
        else:
            logger.warning("CONFIG: No API key found in environment")
    return ModelConfigManager(litellm_url=litellm_url, api_key=api_key)


def handle_list_models(args) -> None:
    """
    Handle list models command.

    Retrieves and displays all models from the LiteLLM database in a
    formatted, user-friendly manner.

    Args:
        args: Command line arguments containing base_url and api_key
    """
    logger.info("COMMAND: Listing models from API")
    manager = create_model_config_manager(args.base_url, args.api_key)
    models = manager.list_models()
    if models:
        print(f"\nFound {len(models)} models in LiteLLM database:")
        print("=" * 60)
        for i, model in enumerate(models, 1):
            model_id = (
                model.get("model_id")
                or model.get("id")
                or (
                    model.get("model_info", {}).get("id")
                    if isinstance(model.get("model_info"), dict)
                    else "N/A"
                )
            )
            model_name = model.get("model_name", "N/A")
            print(f"{i:2d}. {model_name}")
            print(f"    ID: {model_id}")
            if "model_info" in model:
                info = model["model_info"]
                if isinstance(info, dict) and info:
                    info_copy = {k: v for k, v in info.items() if k != "id"}
                    if info_copy:
                        print(f"    Info: {json.dumps(info_copy, indent=6)[6:]}")
            if i < len(models):
                print()
        print("=" * 60)
    else:
        print("No models found in database or API error")


def handle_add_models(args) -> None:
    """
    Handle add models command.

    Loads models from the specified configuration file and adds them to
    the LiteLLM database via the API.

    Args:
        args: Command line arguments containing config_file, base_url, and api_key
    """
    config_file = args.config_file
    logger.info(f"COMMAND: Adding models from configuration: {config_file}")
    if not os.path.exists(config_file):
        print(f"Configuration file not found: {config_file}")
        sys.exit(1)
    print(f"Loading configuration from: {config_file}")
    manager = create_model_config_manager(args.base_url, args.api_key)
    success = manager.add_models_from_config(config_file)
    if success:
        print("All models added successfully!")
    else:
        print("Some models failed to add. Check logs for details.")
        sys.exit(1)


def handle_clear_models(args) -> None:
    """
    Handle clear models command.

    Deletes all models from the LiteLLM database with optional confirmation
    prompt for safety.

    Args:
        args: Command line arguments containing yes flag, base_url, and api_key
    """
    logger.info("COMMAND: Clearing all models from database")
    if not args.yes:
        response = input(
            "WARNING: This will delete ALL models from the database. Continue? [y/N]: "
        )
        if response.lower() not in ["y", "yes"]:
            print("Operation cancelled.")
            return
    print("Clearing all models from database...")
    manager = create_model_config_manager(args.base_url, args.api_key)
    success = manager.clear_all_models()
    if success:
        print("All models cleared successfully!")
    else:
        print("Some models failed to delete. Check logs for details.")
        sys.exit(1)


def handle_update_models(args) -> None:
    """
    Handle update models command.

    Replaces all models in the database with models from the configuration
    file by clearing existing models and adding new ones.

    Args:
        args: Command line arguments containing config_file, yes flag, base_url, and api_key
    """
    config_file = args.config_file
    logger.info(f"COMMAND: Updating models from configuration: {config_file}")
    if not os.path.exists(config_file):
        print(f"Configuration file not found: {config_file}")
        sys.exit(1)
    if not args.yes:
        response = input(
            "WARNING: This will replace ALL models in the database. Continue? [y/N]: "
        )
        if response.lower() not in ["y", "yes"]:
            print("Operation cancelled.")
            return
    print(f"Updating models from configuration: {config_file}")
    manager = create_model_config_manager(args.base_url, args.api_key)
    success = manager.update_models_from_config(config_file)
    if success:
        print("Models updated successfully!")
    else:
        print("Model update failed. Check logs for details.")
        sys.exit(1)


def main():
    """
    Main entry point for the model configuration manager.

    Provides command-line interface for managing LiteLLM models through the
    /model/new API. Supports listing, adding, clearing, and updating models
    with comprehensive error handling and user confirmation for destructive operations.

    Commands:
        - list: Display all models from the database
        - add-models: Add models from YAML configuration
        - clear-models: Delete all models from database
        - update-models: Replace all models with configuration

    Environment Variables:
        LITELLM_MASTER_KEY: API key for LiteLLM authentication
    """
    parser = argparse.ArgumentParser(
        description="LiteLLM Model Configuration Manager - Uses /model/new API for proper model management",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="\nExamples:\n  %(prog)s list                                    # List all models\n  %(prog)s add-models config.yaml                  # Add models from config\n  %(prog)s clear-models --yes                      # Clear all models (no prompt)\n  %(prog)s update-models config.yaml --yes         # Update all models (no prompt)\n  %(prog)s list --base-url http://localhost:4000   # Use custom URL\n        ",
    )
    parser.add_argument(
        "--base-url", default=None, help="LiteLLM API base URL (default: auto-detect)"
    )
    parser.add_argument(
        "--api-key",
        default=None,
        help="API key for authentication (default: LITELLM_MASTER_KEY env var)",
    )
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    list_parser = subparsers.add_parser("list", help="List all models from API")
    add_parser = subparsers.add_parser(
        "add-models", help="Add models from YAML configuration"
    )
    add_parser.add_argument("config_file", help="Path to YAML configuration file")
    clear_parser = subparsers.add_parser(
        "clear-models", help="Delete all models from database"
    )
    clear_parser.add_argument(
        "--yes", action="store_true", help="Skip confirmation prompt"
    )
    update_parser = subparsers.add_parser(
        "update-models", help="Update models from YAML configuration"
    )
    update_parser.add_argument("config_file", help="Path to YAML configuration file")
    update_parser.add_argument(
        "--yes", action="store_true", help="Skip confirmation prompt"
    )
    args = parser.parse_args()
    if not args.command:
        parser.print_help()
        sys.exit(1)
    try:
        if args.command == "list":
            handle_list_models(args)
        elif args.command == "add-models":
            handle_add_models(args)
        elif args.command == "clear-models":
            handle_clear_models(args)
        elif args.command == "update-models":
            handle_update_models(args)
        else:
            print(f"Unknown command: {args.command}")
            parser.print_help()
            sys.exit(1)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"FATAL: {e}")
        logger.debug(f"FATAL: Traceback: {traceback.format_exc()}")
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
