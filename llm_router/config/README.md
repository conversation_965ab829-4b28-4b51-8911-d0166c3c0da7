# Конфигурация метрик LLM Router

Этот модуль обеспечивает автоматическую настройку метрик Prometheus для LLM Router.

## 🚀 Быстрый старт

### Запуск в Docker (рекомендуется)

1. **Синхронизация и сборка контейнера:**
```bash
./run.sh sync
```

2. **Запуск LLM Router с метриками:**
```bash
docker exec -it llm_router python -m llm_router
```

3. **Проверка метрик:**
```bash
curl http://localhost:4000/metrics
```

### Локальный запуск (для разработки)

```bash
python -m llm_router
```

Метрики будут доступны по адресу: `http://localhost:4000/metrics`

## ⚙️ Переменные окружения

### Для Docker

```bash
# Установка переменных перед запуском контейнера
export LLM_ROUTER_PROMETHEUS_METRICS=true
export LLM_ROUTER_SYSTEM_METRICS=true
export LLM_ROUTER_END_USER_TRACKING=false
export LLM_ROUTER_METRICS_DEBUG=false

# Затем запуск
./run.sh sync
docker exec -it llm_router python -m llm_router
```

### Для локального запуска

```bash
# Основные метрики Prometheus (по умолчанию: true)
export LLM_ROUTER_PROMETHEUS_METRICS=true

# Системные метрики Redis/PostgreSQL (по умолчанию: true)
export LLM_ROUTER_SYSTEM_METRICS=true

# Отслеживание end_user (по умолчанию: false)
export LLM_ROUTER_END_USER_TRACKING=false

# Debug режим (по умолчанию: false)
export LLM_ROUTER_METRICS_DEBUG=false
```

## 🔧 Программное управление

```python
from llm_router.config.metrics_config import enable_metrics_programmatically

# Включение метрик
result = enable_metrics_programmatically(
    prometheus=True,
    system_metrics=True,
    end_user_tracking=False,
    debug=False
)
```

## 📊 Проверка статуса

### В Docker контейнере

```bash
# Проверка метрик через curl
curl http://localhost:4000/metrics

# Или программная проверка
docker exec -it llm_router python -c "
from llm_router.config.metrics_config import verify_metrics_setup
print('✅ Метрики работают' if verify_metrics_setup() else '❌ Метрики не настроены')
"
```

### Локально

```python
from llm_router.config.metrics_config import verify_metrics_setup

if verify_metrics_setup():
    print("✅ Метрики настроены и работают")
```

## 📚 Документация

- [Полное руководство](../docs/METRICS_CONFIGURATION_GUIDE.md)
- [Примеры использования](metrics_example.py)

## 📁 Файлы модуля

- `metrics_config.py` - основная конфигурация метрик
- `metrics_example.py` - примеры использования
- `README.md` - этот файл

## 🔍 Отладка

### В Docker

```bash
# Включение debug режима
export LLM_ROUTER_METRICS_DEBUG=true
./run.sh sync

# Запуск с отладкой
docker exec -it llm_router python -m llm_router

# Проверка логов
docker logs llm_router | grep METRICS
```

### Локально

Для включения отладочных логов:

```bash
export LLM_ROUTER_METRICS_DEBUG=true
```

Или программно:

```python
from llm_router.config.metrics_config import enable_metrics_programmatically

enable_metrics_programmatically(debug=True)
```

## 🐳 Docker Compose пример

```yaml
version: '3.8'
services:
  llm-router:
    build: .
    ports:
      - "4000:4000"
    environment:
      - LLM_ROUTER_PROMETHEUS_METRICS=true
      - LLM_ROUTER_SYSTEM_METRICS=true
      - LLM_ROUTER_END_USER_TRACKING=false
      - LLM_ROUTER_METRICS_DEBUG=false
    
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
```
