#!/usr/bin/env python3
"""
Host Utilities for LiteLLM Router.

Common utilities for scripts that run on the host (not in Docker).
"""

import logging
import os
from pathlib import Path
from typing import Optional

# Simple logging setup
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s:%(levelname)s: %(filename)s:%(lineno)d - %(message)s",
)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance."""
    return logging.getLogger(name)


def load_env_file(env_file: str = ".env") -> None:
    """Load environment variables from .env file."""
    if not os.path.exists(env_file):
        raise FileNotFoundError(f".env file not found: {env_file}")

    with open(env_file, "r") as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith("#") and "=" in line:
                key, value = line.split("=", 1)
                # Remove quotes if present
                value = value.strip("'\"")
                os.environ[key] = value


def get_env_variable(name: str, default: Optional[str] = None) -> Optional[str]:
    """Get environment variable with optional default."""
    return os.environ.get(name, default)


def update_env_file(
    key: str,
    value: str,
    env_file: str = ".env",
    create_backup: bool = True,
    quote_value: bool = True,
) -> bool:
    """
    Update a key-value pair in .env file.

    Args:
        key: Environment variable name
        value: Environment variable value
        env_file: Path to .env file
        create_backup: Whether to create backup before modification
        quote_value: Whether to wrap value in single quotes

    Returns:
        True if successful, False otherwise
    """
    logger = get_logger(__name__)

    try:
        env_path = Path(env_file)

        # Create backup
        if create_backup and env_path.exists():
            # Create backup directory if it doesn't exist
            backup_dir = env_path.parent / "backup"
            backup_dir.mkdir(exist_ok=True)

            # Create proper backup filename with timestamp
            import time

            timestamp = time.strftime("%Y%m%d_%H%M%S")
            backup_path = backup_dir / f"{env_path.name}.backup.{timestamp}"
            backup_path.write_text(env_path.read_text())
            logger.info(f"Created backup: {backup_path}")

        # Read existing content
        lines = []
        if env_path.exists():
            lines = env_path.read_text().splitlines()

        # Format value
        formatted_value = f"'{value}'" if quote_value else value
        new_line = f"{key}={formatted_value}"

        # Replace existing key or add at the end
        key_replaced = False
        for i, line in enumerate(lines):
            if line.startswith(f"{key}="):
                lines[i] = new_line
                key_replaced = True
                break

        # If no existing key found, add at the end
        if not key_replaced:
            lines.append(new_line)

        # Write back
        env_path.write_text("\n".join(lines) + "\n")

        logger.info(f"Updated {env_file} with new {key}")
        return True

    except Exception as e:
        logger.error(f"Failed to update {env_file}: {e}")
        return False


def setup_env_for_host_script(env_file: Optional[str] = None) -> None:
    """
    Setup environment for host scripts.

    Loads .env file from appropriate location (current dir or parent dir).
    """
    logger = get_logger(__name__)

    try:
        # Auto-detect .env file location
        if env_file is None:
            if os.path.exists("../.env"):
                env_file = "../.env"
            elif os.path.exists(".env"):
                env_file = ".env"
            else:
                logger.warning("No .env file found, using system environment variables")
                return

        load_env_file(env_file)
        logger.debug(f"Loaded environment from {env_file}")

    except FileNotFoundError:
        logger.warning("No .env file found, using system environment variables")
