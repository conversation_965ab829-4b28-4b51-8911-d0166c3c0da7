#!/usr/bin/env python3
from pathlib import Path
from typing import Dict, Any, Optional
import argparse
import json
import os
import sys
import traceback

from llm_router.logging_utils import get_logger
from llm_router.router_litellm.litellm_config_api import (
    LiteLLMConfigAPI,
    LiteLLMConfigAPIError,
    load_yaml_config,
    format_config_for_api,
)

"""
LiteLLM Configuration Manager.

This script provides utilities for managing LiteLLM configuration
and updating configuration from YAML files.

Commands:
- list: Show current configuration from database
- update: Update configuration from YAML file to database
"""

current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))
logger = get_logger(__name__)
LITELLM_BASE_URL = os.getenv("LITELLM_API_BASE", "http://localhost:4000")


def list_config(api_client: LiteLLMConfigAPI) -> None:
    """
    List current LiteLLM configuration from database.

    Args:
        api_client: Configured LiteLLM Config API client
    """
    try:
        logger.info("CONFIG: Retrieving current configuration from database")
        config = api_client.get_config()
        print("=" * 80)
        print("CURRENT LITELLM CONFIGURATION (from database)")
        print("=" * 80)
        main_sections = [
            "general_settings",
            "litellm_settings",
            "environment_variables",
        ]
        for section in main_sections:
            print(f"\n {section.upper().replace('_', ' ')}:")
            if section in config and config[section]:
                print(json.dumps(config[section], indent=2, ensure_ascii=False))
            else:
                print("   (not configured)")
        other_sections = {k: v for k, v in config.items() if k not in main_sections}
        if other_sections:
            print(f"\n[INFO] OTHER SECTIONS:")
            for section, data in other_sections.items():
                print(f"\n   {section}:")
                if isinstance(data, (dict, list)):
                    print(json.dumps(data, indent=4, ensure_ascii=False))
                else:
                    print(f"   {data}")
        print("\n" + "=" * 80)
        logger.info("CONFIG: Configuration listing completed")
    except LiteLLMConfigAPIError as e:
        logger.error(f"CONFIG: API error: {e}")
        print(f"Error retrieving configuration: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"CONFIG: Unexpected error: {e}")
        print(f"Unexpected error: {e}")
        sys.exit(1)


def update_config(
    api_client: LiteLLMConfigAPI, config_file_path: str, auto_confirm: bool = False
) -> None:
    """
    Update LiteLLM configuration from YAML file to database.

    Args:
        api_client: Configured LiteLLM Config API client
        config_file_path: Path to YAML configuration file
        auto_confirm: Skip confirmation prompt if True
    """
    try:
        logger.info(f"CONFIG: Loading configuration from {config_file_path}")
        yaml_config = load_yaml_config(config_file_path)
        if not yaml_config:
            print("Configuration file is empty or invalid")
            sys.exit(1)
        print(f"Loaded configuration from: {config_file_path}")
        print(f"Configuration sections found: {list(yaml_config.keys())}")
        formatted_config = format_config_for_api(yaml_config)
        if not formatted_config:
            print("No valid configuration sections found to update")
            sys.exit(1)
        print(f"Sections to update: {list(formatted_config.keys())}")

        if not auto_confirm:
            print("\n" + "=" * 60)
            print(
                "WARNING: This will update the LiteLLM configuration in the database."
            )
            print(
                "   Current configuration will be overwritten for the specified sections."
            )
            print("=" * 60)
            try:
                response = input("\nProceed with configuration update? [y/N]: ")
                if response.lower() not in ("y", "yes"):
                    print("Configuration update cancelled")
                    return
            except EOFError:
                logger.warning(
                    "CONFIG: Cannot read input (running in non-interactive mode)"
                )
                print(
                    "Configuration update cancelled - use --yes flag for non-interactive mode"
                )
                return
        else:
            print("\nAuto-confirming configuration update (--yes flag used)")

        logger.info("CONFIG: Updating configuration via API")
        result = api_client.update_config(formatted_config)
        print(f"Configuration updated successfully!")
        print(f"API Response: {result.get('message', 'Success')}")
        print(f"\nUpdated sections:")
        for section in formatted_config.keys():
            print(f"   - {section}")
        print(f"\nThe router will use the new configuration for future requests.")
        print(
            f"   You may need to restart the LiteLLM service for all changes to take effect."
        )
        logger.info("CONFIG: Configuration update completed successfully")
    except FileNotFoundError as e:
        logger.error(f"CONFIG: File not found: {e}")
        print(f"Configuration file not found: {e}")
        sys.exit(1)
    except LiteLLMConfigAPIError as e:
        logger.error(f"CONFIG: API error: {e}")
        print(f"Error updating configuration: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"CONFIG: Unexpected error: {e}")
        print(f"Unexpected error: {e}")
        sys.exit(1)


def main():
    """
    Main CLI interface for configuration management.

    Provides command-line interface for managing LiteLLM configuration including
    listing current configuration and updating from YAML files.

    Supported commands:
        - list: Show current configuration from database
        - update: Update configuration from YAML file to database

    Environment Variables:
        LITELLM_MASTER_KEY: LiteLLM Master API key for authentication

    Examples:
        python config_manager.py list
        python config_manager.py update litellm.config.yaml
    """
    parser = argparse.ArgumentParser(
        description="LiteLLM Configuration Manager",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="\nExamples:\n  %(prog)s list                              # Show current config from database\n  %(prog)s update litellm.config.yaml       # Update config from YAML file\n        ",
    )
    parser.add_argument(
        "command",
        choices=["list", "update"],
        help="Configuration management command",
    )
    parser.add_argument(
        "config_file",
        nargs="?",
        help="Path to YAML configuration file (required for update commands)",
    )
    parser.add_argument(
        "--api-key",
        help="LiteLLM Master API key (overrides LITELLM_MASTER_KEY env var)",
    )
    parser.add_argument(
        "--base-url", help="LiteLLM proxy base URL (auto-detected if not provided)"
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )
    parser.add_argument(
        "--yes", "-y", action="store_true", help="Skip confirmation prompt"
    )
    args = parser.parse_args()
    if args.verbose:
        import logging

        logging.getLogger().setLevel(logging.DEBUG)
    if args.command == "update" and (not args.config_file):
        parser.error(f"Configuration file is required for '{args.command}' command")
    if args.config_file and (not Path(args.config_file).exists()):
        print(f"Configuration file not found: {args.config_file}")
        sys.exit(1)
    try:
        if not args.base_url:
            args.base_url = LITELLM_BASE_URL
            logger.info(f"CONFIG: Auto-detected LiteLLM URL: {args.base_url}")
        if args.api_key:
            api_client = LiteLLMConfigAPI(base_url=args.base_url, api_key=args.api_key)
        else:
            env_api_key = os.getenv("LITELLM_MASTER_KEY")
            if env_api_key:
                logger.info("CONFIG: Using LITELLM_MASTER_KEY from environment")
                api_client = LiteLLMConfigAPI(
                    base_url=args.base_url, api_key=env_api_key
                )
            else:
                logger.warning("CONFIG: No API key found in environment or arguments")
                api_client = LiteLLMConfigAPI(base_url=args.base_url)
        if args.command == "list":
            list_config(api_client)
        elif args.command == "update":
            update_config(api_client, args.config_file, args.yes)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"CONFIG: Fatal error: {e}")
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
