# System Validation Tests

## Описание

Модуль `test_system_validation.py` предоставляет комплексные тесты для валидации системы LiteLLM, включая тестирование класса `SystemValidator` и основных функций системы.

## Файлы

### `test_system_validation.py`
Основной тестовый модуль с поддержкой pytest и простого режима.

**Особенности:**
- ✅ Работает без зависимостей (graceful degradation)
- ✅ Поддерживает pytest и простой режим
- ✅ Исправлены все ошибки типизации
- ✅ Комплексное тестирование системы

### `test_system_validation_simple.py`
Упрощенная версия теста без pytest зависимостей.

## Как запускать

### Простой режим (без pytest)
```bash
# Из папки tests/
python3 test_system_validation.py --simple

# Или с параметрами
python3 test_system_validation.py --simple --base-url http://localhost:4000 --api-key sk-1234
```

### Pytest режим
```bash
# Из папки tests/
python3 test_system_validation.py --pytest -v

# Или напрямую через pytest
pytest test_system_validation.py -v
```

### Упрощенная версия
```bash
# Из папки tests/
python3 test_system_validation_simple.py
```

## Что тестируется

### 1. Основные тесты системы
- **Proxy connectivity** - подключение к LiteLLM прокси
- **Models availability** - доступность моделей
- **Health check** - проверка здоровья системы
- **Chat completion** - тестирование чат-запросов

### 2. SystemValidator класс (если доступны зависимости)
- **check_proxy_health()** - проверка здоровья прокси
- **get_models_list()** - получение списка моделей
- **get_model_info()** - получение информации о моделях
- **run_health_checks()** - комплексная проверка здоровья
- **test_chat_completion()** - тестирование чат-запросов

### 3. Интеграционный тест
- Комплексный тест всех компонентов с детальным отчетом

## Настройка

### Переменные окружения
- `LITELLM_API_BASE` - базовый URL LiteLLM (по умолчанию: http://localhost:4000)
- `LITELLM_MASTER_KEY` - API ключ (по умолчанию: sk-1234)

### Аргументы командной строки
- `--simple` - запуск в простом режиме
- `--pytest` - запуск через pytest
- `--base-url URL` - указать базовый URL
- `--api-key KEY` - указать API ключ
- `-v, --verbose` - подробный вывод

## Примеры вывода

### Успешный тест
```
=== Simple System Validation Test ===

1. Testing proxy readiness...
   ✓ Proxy ready: connected
   ✓ Database: connected
   ✓ Version: 1.70.4

2. Testing models availability...
   ✓ Found 2 models:
      - chat_instruct
      - chat_coder

3. Testing system health...
   ✓ Health check: 6 healthy, 0 unhealthy

4. Testing chat completion...
   ✓ Chat completion with chat_instruct successful
   Response: Hello! How can I assist you today?

=== Test Summary ===
Tests passed: 4/4
🎉 All tests passed! System is healthy.
```

### Pytest вывод
```
============================= test session starts ==============================
platform linux -- Python 3.12.3, pytest-7.4.4, pluggy-1.4.0
collected 6 items

test_system_validation.py::TestSystemValidation::test_proxy_connectivity PASSED
test_system_validation.py::TestSystemValidation::test_models_availability PASSED
test_system_validation.py::TestSystemValidation::test_health_check PASSED
test_system_validation.py::TestSystemValidation::test_chat_completion PASSED
test_system_validation.py::TestSystemValidation::test_system_validator_class SKIPPED
test_system_validation.py::TestSystemValidation::test_integration_comprehensive PASSED

========================= 5 passed, 1 skipped in 2.09s =========================
```

## Интеграция с run.sh

Тесты готовы к интеграции в систему команд `./run.sh test`:

```bash
# Будущие команды (после интеграции)
./run.sh test system-validation simple      # Простой тест
./run.sh test system-validation pytest      # Pytest тест
./run.sh test system-validation comprehensive # Полная валидация
```

## Обработка ошибок

### Отсутствие зависимостей
Если модули `llm_router` недоступны, тесты:
- ✅ Показывают предупреждение
- ✅ Пропускают SystemValidator тесты
- ✅ Продолжают работу с основными тестами

### Недоступность сервиса
Если LiteLLM недоступен:
- ❌ Тесты завершаются с ошибкой
- 📊 Предоставляется детальная информация об ошибке

### Неверные модели
Если модели недоступны:
- ⚠ Соответствующие тесты пропускаются
- 📝 Выводится информативное сообщение

## Технические детали

### Исправления типизации
- ✅ Решена проблема с `SystemValidator` is possibly unbound
- ✅ Решена проблема с `Object of type "None" is not subscriptable`
- ✅ Добавлены type assertions для помощи type checker
- ✅ Убраны return значения из pytest тестов

### Архитектура
- 🔧 Класс `TestSystemValidation` для pytest тестов
- 🔧 Функция `simple_validation_test()` для простого режима
- 🔧 Хелпер методы `_get_test_models()` и `_get_health_data()`
- 🔧 Graceful degradation при отсутствии зависимостей
