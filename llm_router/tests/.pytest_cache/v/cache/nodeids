["test_system_validation.py::TestSystemValidation::test_chat_completion", "test_system_validation.py::TestSystemValidation::test_health_check", "test_system_validation.py::TestSystemValidation::test_integration_comprehensive", "test_system_validation.py::TestSystemValidation::test_models_availability", "test_system_validation.py::TestSystemValidation::test_proxy_connectivity", "test_system_validation.py::TestSystemValidation::test_system_validator_class"]