#!/usr/bin/env python3
"""
Простой тест для проверки функциональности task_type_utils внутри Docker контейнера.
"""

import sys
import os

# Добавляем пути для импорта
sys.path.insert(0, '/app')
sys.path.insert(0, '/app/llm_router')

try:
    from router.task_type_utils import (
        get_task_type_from_model_name,
        is_valid_task_type,
        get_supported_task_types
    )
    from constants import DEFAULT_TASK_TYPE, SUPPORTED_TASK_TYPES
    
    print("✓ Импорты успешны")
    
    # Тестируем основную функциональность
    print("\n=== Тестирование get_task_type_from_model_name ===")
    
    test_cases = [
        ("chat_instruct", "chat"),
        ("chat_coder", "code"),
        ("unknown_model", DEFAULT_TASK_TYPE),
        ("", DEFAULT_TASK_TYPE),
        ("my_coder_model", "code"),
        ("chat_model_v2", "chat"),
        ("instruct_model", "chat"),
    ]
    
    for model_name, expected in test_cases:
        result = get_task_type_from_model_name(model_name)
        status = "✓" if result == expected else "✗"
        print(f"{status} {model_name!r} -> {result!r} (expected: {expected!r})")
    
    print("\n=== Тестирование is_valid_task_type ===")
    
    valid_tests = [
        ("chat", True),
        ("code", True),
        ("invalid", False),
        ("", False),
    ]
    
    for task_type, expected in valid_tests:
        result = is_valid_task_type(task_type)
        status = "✓" if result == expected else "✗"
        print(f"{status} is_valid_task_type({task_type!r}) -> {result} (expected: {expected})")
    
    print("\n=== Тестирование get_supported_task_types ===")
    
    supported = get_supported_task_types()
    print(f"✓ get_supported_task_types() -> {supported}")
    print(f"✓ Содержит 'chat': {'chat' in supported}")
    print(f"✓ Содержит 'code': {'code' in supported}")
    
    print("\n=== Тестирование констант ===")
    print(f"✓ DEFAULT_TASK_TYPE = {DEFAULT_TASK_TYPE!r}")
    print(f"✓ SUPPORTED_TASK_TYPES = {SUPPORTED_TASK_TYPES}")
    
    print("\n🎉 Все тесты прошли успешно!")
    
except ImportError as e:
    print(f"✗ Ошибка импорта: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ Ошибка выполнения: {e}")
    sys.exit(1)