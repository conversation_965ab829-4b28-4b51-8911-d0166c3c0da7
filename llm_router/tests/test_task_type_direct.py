#!/usr/bin/env python3
"""
Прямой тест task_type_utils без pytest для проверки в Docker контейнере.
"""

import sys
import os
sys.path.insert(0, '.')

def test_task_type_utils():
    """Тест функциональности task_type_utils."""
    print("=== TESTING TASK TYPE UTILS ===")
    
    try:
        # Импортируем константы
        from constants import (
            MODEL_NAME_TO_TASK_TYPE_MAPPING,
            DEFAULT_TASK_TYPE,
            SUPPORTED_TASK_TYPES,
        )
        print("✓ Constants imported successfully")
        print(f"  DEFAULT_TASK_TYPE: {DEFAULT_TASK_TYPE}")
        print(f"  SUPPORTED_TASK_TYPES: {SUPPORTED_TASK_TYPES}")
        print(f"  MODEL_NAME_TO_TASK_TYPE_MAPPING: {MODEL_NAME_TO_TASK_TYPE_MAPPING}")
        
        # Импортируем функции task_type_utils
        from router.task_type_utils import (
            get_task_type_from_model_name,
            is_valid_task_type,
            get_supported_task_types
        )
        print("✓ Task type utils functions imported successfully")
        
        # Тестируем известные модели
        test_cases = [
            ("chat_instruct", "chat"),
            ("chat_coder", "code"),
            ("unknown_model", DEFAULT_TASK_TYPE),
            ("", DEFAULT_TASK_TYPE),
            ("model_with_chat", "chat"),
            ("model_with_code", "code"),
            ("coder_model", "code"),
            ("instruct_model", "chat"),
        ]
        
        print("\n--- Testing get_task_type_from_model_name ---")
        all_passed = True
        for model_name, expected in test_cases:
            result = get_task_type_from_model_name(model_name)
            status = "✓" if result == expected else "✗"
            print(f"  {status} {model_name!r} -> {result!r} (expected {expected!r})")
            if result != expected:
                all_passed = False
        
        # Тестируем валидацию типов задач
        print("\n--- Testing is_valid_task_type ---")
        validation_cases = [
            ("chat", True),
            ("code", True),
            ("invalid", False),
            ("", False),
            (None, False),
        ]
        
        for task_type, expected in validation_cases:
            try:
                result = is_valid_task_type(task_type)
                status = "✓" if result == expected else "✗"
                print(f"  {status} {task_type!r} -> {result} (expected {expected})")
                if result != expected:
                    all_passed = False
            except Exception as e:
                print(f"  ✗ {task_type!r} -> ERROR: {e}")
                all_passed = False
        
        # Тестируем получение поддерживаемых типов
        print("\n--- Testing get_supported_task_types ---")
        supported = get_supported_task_types()
        print(f"  ✓ Supported types: {supported}")
        if not isinstance(supported, set):
            print(f"  ✗ Expected set, got {type(supported)}")
            all_passed = False
        if "chat" not in supported or "code" not in supported:
            print(f"  ✗ Missing expected types in {supported}")
            all_passed = False
        
        # Тестируем интеграционные сценарии
        print("\n--- Testing integration scenarios ---")
        integration_cases = [
            ("gpt-3.5-turbo-instruct", "chat"),
            ("codellama-7b-instruct", "chat"),
            ("codellama-7b-code", "code"),
            ("deepseek-coder-6.7b", "code"),
            ("mistral-7b-instruct-v0.1", "chat"),
            ("wizardcoder-15b", "code"),
            ("vicuna-13b-chat", "chat"),
            ("starcoder-15b", "code"),
            ("llama-2-70b-chat", "chat"),
            ("gpt-4", DEFAULT_TASK_TYPE),  # fallback
        ]
        
        for model_name, expected in integration_cases:
            result = get_task_type_from_model_name(model_name)
            status = "✓" if result == expected else "✗"
            print(f"  {status} {model_name!r} -> {result!r} (expected {expected!r})")
            if result != expected:
                all_passed = False
        
        if all_passed:
            print("\n🎉 ALL TESTS PASSED!")
            return True
        else:
            print("\n❌ SOME TESTS FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_task_type_utils()
    sys.exit(0 if success else 1)