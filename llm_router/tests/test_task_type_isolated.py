#!/usr/bin/env python3
"""
Изолированный тест для task_type_utils без зависимостей от других модулей.
"""

import sys
import os

# Добавляем пути для импорта
sys.path.insert(0, '/app/llm_router')

# Импортируем константы напрямую
from constants import MODEL_NAME_TO_TASK_TYPE_MAPPING, DEFAULT_TASK_TYPE, SUPPORTED_TASK_TYPES

# Копируем код функций для изолированного тестирования
def get_task_type_from_model_name(model_name: str) -> str:
    """
    Определяет тип задачи на основе имени модели.
    """
    if not model_name:
        return DEFAULT_TASK_TYPE
        
    # Прямое соответствие
    if model_name in MODEL_NAME_TO_TASK_TYPE_MAPPING:
        return MODEL_NAME_TO_TASK_TYPE_MAPPING[model_name]
    
    # Поиск по подстрокам для гибкости
    model_name_lower = model_name.lower()
    
    # Проверяем приоритет: instruct и chat имеют приоритет над code
    if "instruct" in model_name_lower or "chat" in model_name_lower:
        return "chat"
    elif "coder" in model_name_lower or "code" in model_name_lower:
        return "code"
    
    return DEFAULT_TASK_TYPE


def is_valid_task_type(task_type: str) -> bool:
    """
    Проверяет валидность типа задачи.
    """
    return task_type in SUPPORTED_TASK_TYPES


def get_supported_task_types() -> set:
    """
    Возвращает множество поддерживаемых типов задач.
    """
    return SUPPORTED_TASK_TYPES.copy()


def run_comprehensive_tests():
    """Запускает комплексные тесты функциональности."""
    
    print("🧪 Запуск комплексных тестов task_type_utils")
    print("=" * 50)
    
    # Тест 1: Известные модели
    print("\n1. Тестирование известных моделей:")
    known_tests = [
        ("chat_instruct", "chat"),
        ("chat_coder", "code"),
    ]
    
    for model_name, expected in known_tests:
        result = get_task_type_from_model_name(model_name)
        status = "✓" if result == expected else "✗"
        print(f"   {status} {model_name!r} -> {result!r}")
        assert result == expected, f"Expected {expected}, got {result}"
    
    # Тест 2: Пустые и None значения
    print("\n2. Тестирование пустых значений:")
    empty_tests = [
        ("", DEFAULT_TASK_TYPE),
        (None, DEFAULT_TASK_TYPE),
    ]
    
    for model_name, expected in empty_tests:
        result = get_task_type_from_model_name(model_name)
        status = "✓" if result == expected else "✗"
        print(f"   {status} {model_name!r} -> {result!r}")
        assert result == expected, f"Expected {expected}, got {result}"
    
    # Тест 3: Частичные совпадения для code
    print("\n3. Тестирование частичных совпадений (code):")
    code_tests = [
        ("my_coder_model", "code"),
        ("advanced_coder", "code"),
        ("CODER_MODEL", "code"),
        ("code_model", "code"),
        ("model_code_v2", "code"),
        ("CODE_ASSISTANT", "code"),
    ]
    
    for model_name, expected in code_tests:
        result = get_task_type_from_model_name(model_name)
        status = "✓" if result == expected else "✗"
        print(f"   {status} {model_name!r} -> {result!r}")
        assert result == expected, f"Expected {expected}, got {result}"
    
    # Тест 4: Частичные совпадения для chat
    print("\n4. Тестирование частичных совпадений (chat):")
    chat_tests = [
        ("chat_model_v2", "chat"),
        ("advanced_chat", "chat"),
        ("CHAT_ASSISTANT", "chat"),
        ("instruct_model", "chat"),
        ("model_instruct_v1", "chat"),
        ("INSTRUCT_GPT", "chat"),
    ]
    
    for model_name, expected in chat_tests:
        result = get_task_type_from_model_name(model_name)
        status = "✓" if result == expected else "✗"
        print(f"   {status} {model_name!r} -> {result!r}")
        assert result == expected, f"Expected {expected}, got {result}"
    
    # Тест 5: Приоритет chat/instruct над code
    print("\n5. Тестирование приоритета chat/instruct над code:")
    priority_tests = [
        ("chat_coder_model", "chat"),  # chat имеет приоритет
        ("instruct_code_model", "chat"),  # instruct имеет приоритет
        ("code_chat_hybrid", "chat"),  # chat имеет приоритет
        ("pure_coder_model", "code"),  # только coder -> code
        ("pure_code_model", "code"),  # только code -> code
    ]
    
    for model_name, expected in priority_tests:
        result = get_task_type_from_model_name(model_name)
        status = "✓" if result == expected else "✗"
        print(f"   {status} {model_name!r} -> {result!r}")
        assert result == expected, f"Expected {expected}, got {result}"
    
    # Тест 6: Неизвестные модели
    print("\n6. Тестирование неизвестных моделей:")
    unknown_tests = [
        ("unknown_model", DEFAULT_TASK_TYPE),
        ("random_name", DEFAULT_TASK_TYPE),
        ("model_v1", DEFAULT_TASK_TYPE),
        ("gpt-4", DEFAULT_TASK_TYPE),
    ]
    
    for model_name, expected in unknown_tests:
        result = get_task_type_from_model_name(model_name)
        status = "✓" if result == expected else "✗"
        print(f"   {status} {model_name!r} -> {result!r}")
        assert result == expected, f"Expected {expected}, got {result}"
    
    # Тест 7: Валидация типов задач
    print("\n7. Тестирование валидации типов задач:")
    validation_tests = [
        ("chat", True),
        ("code", True),
        ("invalid", False),
        ("", False),
        ("CHAT", False),
        ("CODE", False),
    ]
    
    for task_type, expected in validation_tests:
        result = is_valid_task_type(task_type)
        status = "✓" if result == expected else "✗"
        print(f"   {status} is_valid_task_type({task_type!r}) -> {result}")
        assert result == expected, f"Expected {expected}, got {result}"
    
    # Тест 8: Поддерживаемые типы задач
    print("\n8. Тестирование get_supported_task_types:")
    supported = get_supported_task_types()
    print(f"   ✓ Возвращает: {supported}")
    assert "chat" in supported, "Should contain 'chat'"
    assert "code" in supported, "Should contain 'code'"
    assert len(supported) >= 2, "Should contain at least 2 types"
    
    # Проверяем что это копия
    original_size = len(supported)
    supported.add("test_type")
    new_supported = get_supported_task_types()
    assert len(new_supported) == original_size, "Should return a copy, not reference"
    print("   ✓ Возвращает копию, а не ссылку")
    
    # Тест 9: Реальные сценарии
    print("\n9. Тестирование реальных сценариев:")
    real_world_tests = [
        ("gpt-3.5-turbo-instruct", "chat"),
        ("codellama-7b-instruct", "chat"),  # instruct имеет приоритет
        ("codellama-7b-code", "code"),
        ("claude-3-haiku-chat", "chat"),
        ("deepseek-coder-6.7b", "code"),
        ("mistral-7b-instruct-v0.1", "chat"),
        ("wizardcoder-15b", "code"),
        ("vicuna-13b-chat", "chat"),
        ("starcoder-15b", "code"),
        ("llama-2-70b-chat", "chat"),
    ]
    
    for model_name, expected in real_world_tests:
        result = get_task_type_from_model_name(model_name)
        status = "✓" if result == expected else "✗"
        print(f"   {status} {model_name!r} -> {result!r}")
        assert result == expected, f"Expected {expected}, got {result}"
    
    print("\n" + "=" * 50)
    print("🎉 Все тесты прошли успешно!")
    print(f"📊 Протестировано {len(known_tests) + len(empty_tests) + len(code_tests) + len(chat_tests) + len(priority_tests) + len(unknown_tests) + len(validation_tests) + len(real_world_tests) + 1} тестовых случаев")


if __name__ == "__main__":
    try:
        run_comprehensive_tests()
    except Exception as e:
        print(f"\n❌ Тест провален: {e}")
        sys.exit(1)