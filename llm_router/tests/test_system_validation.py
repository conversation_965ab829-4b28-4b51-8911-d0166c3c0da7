#!/usr/bin/env python3
"""
System Validation Tests for LiteLLM.

This module provides comprehensive tests for the SystemValidator class
and related system validation functionality.
"""

import os
import sys
import pytest
import requests
import asyncio
from typing import Dict, Any, List, Tuple
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

try:
    from llm_router.logging_utils import get_logger
    from utils.validate_system import SystemValidator

    DEPENDENCIES_AVAILABLE = True
    logger = get_logger(__name__)
    SystemValidatorClass = SystemValidator
except ImportError as e:
    print(f"Warning: Dependencies not available: {e}")
    DEPENDENCIES_AVAILABLE = False
    logger = None
    SystemValidatorClass = None


class TestSystemValidation:
    """Test class for system validation functionality."""

    @classmethod
    def setup_class(cls):
        """Setup test class."""
        cls.base_url = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
        cls.api_key = os.getenv("LITELLM_MASTER_KEY", "sk-1234")
        cls.timeout = 30

    def test_proxy_connectivity(self):
        """Test basic proxy connectivity."""
        try:
            response = requests.get(f"{self.base_url}/health/readiness", timeout=10)
            assert (
                response.status_code == 200
            ), f"Proxy not ready: {response.status_code}"

            data = response.json()
            assert data.get("status") == "connected", "Proxy status not connected"
            assert data.get("db") == "connected", "Database not connected"

            print(f"✓ Proxy ready: {data.get('litellm_version', 'unknown')}")

        except requests.RequestException as e:
            pytest.fail(f"Proxy connectivity failed: {e}")

    def test_models_availability(self):
        """Test models availability."""
        headers = {"Authorization": f"Bearer {self.api_key}"}

        try:
            response = requests.get(
                f"{self.base_url}/v1/models", headers=headers, timeout=15
            )
            assert (
                response.status_code == 200
            ), f"Models request failed: {response.status_code}"

            data = response.json()
            models = data.get("data", [])
            assert len(models) > 0, "No models available"

            print(f"✓ Found {len(models)} models:")
            for model in models[:3]:
                print(f"  - {model.get('id', 'unknown')}")
            if len(models) > 3:
                print(f"  ... and {len(models) - 3} more")

            # Store models for other tests to use
            self._test_models = models

        except requests.RequestException as e:
            pytest.fail(f"Models availability test failed: {e}")

    def _get_test_models(self):
        """Helper method to get models for other tests."""
        if hasattr(self, "_test_models"):
            return self._test_models

        # Fallback: get models again
        headers = {"Authorization": f"Bearer {self.api_key}"}
        try:
            response = requests.get(
                f"{self.base_url}/v1/models", headers=headers, timeout=15
            )
            if response.status_code == 200:
                data = response.json()
                return data.get("data", [])
        except requests.RequestException:
            pass
        return []

    def test_health_check(self):
        """Test system health check."""
        headers = {"Authorization": f"Bearer {self.api_key}"}

        try:
            response = requests.get(
                f"{self.base_url}/health", headers=headers, timeout=60
            )
            assert (
                response.status_code == 200
            ), f"Health check failed: {response.status_code}"

            data = response.json()
            healthy = data.get("healthy_count", 0)
            unhealthy = data.get("unhealthy_count", 0)

            print(f"✓ Health check: {healthy} healthy, {unhealthy} unhealthy endpoints")

            if unhealthy > 0:
                unhealthy_list = data.get("unhealthy_endpoints", [])
                print("⚠ Unhealthy endpoints:")
                for endpoint in unhealthy_list[:3]:
                    model = endpoint.get("model", "unknown")
                    error = endpoint.get("error", "unknown error")
                    print(f"  - {model}: {error}")

            # Store health data for other tests
            self._health_data = data

        except requests.RequestException as e:
            pytest.fail(f"Health check test failed: {e}")

    def _get_health_data(self):
        """Helper method to get health data for other tests."""
        if hasattr(self, "_health_data"):
            return self._health_data
        return None

    def test_chat_completion(self):
        """Test chat completion functionality."""
        # First get available models
        models = self._get_test_models()
        if not models or len(models) == 0:
            pytest.skip("No models available for chat completion test")

        test_model = models[0].get("id")
        if not test_model:
            pytest.skip("No valid model ID found for chat completion test")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        payload = {
            "model": test_model,
            "messages": [{"role": "user", "content": "Hello, this is a test message."}],
            "max_tokens": 50,
            "temperature": 0.1,
        }

        try:
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=self.timeout,
            )

            assert (
                response.status_code == 200
            ), f"Chat completion failed: {response.status_code}"

            data = response.json()
            content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
            assert len(content) > 0, "Empty response content"

            print(f"✓ Chat completion with {test_model} successful")
            print(f"  Response: {content[:100]}{'...' if len(content) > 100 else ''}")

        except requests.RequestException as e:
            pytest.fail(f"Chat completion test failed: {e}")

    @pytest.mark.skipif(
        not DEPENDENCIES_AVAILABLE, reason="SystemValidator dependencies not available"
    )
    def test_system_validator_class(self):
        """Test SystemValidator class functionality."""
        if not SystemValidatorClass:
            pytest.skip("SystemValidator class not available")

        # Type assertion to help type checker
        assert SystemValidatorClass is not None
        validator = SystemValidatorClass(base_url=self.base_url, api_key=self.api_key)

        # Test proxy health check
        is_healthy, health_info = validator.check_proxy_health()
        assert is_healthy, f"SystemValidator proxy health check failed: {health_info}"
        print("✓ SystemValidator proxy health check passed")

        # Test models list
        success, models = validator.get_models_list()
        assert success, "SystemValidator get_models_list failed"
        assert len(models) > 0, "SystemValidator found no models"
        print(f"✓ SystemValidator found {len(models)} models")

        # Test model info
        success, model_info = validator.get_model_info()
        assert success, "SystemValidator get_model_info failed"
        print(f"✓ SystemValidator retrieved info for {len(model_info)} models")

        # Test health checks
        all_healthy, health_results = validator.run_health_checks()
        print(
            f"✓ SystemValidator health checks: {health_results.get('healthy_count', 0)} healthy endpoints"
        )

        # Test chat completion
        if models and len(models) > 0:
            test_model = models[0].get("id")
            if test_model:
                success, result = validator.test_chat_completion(test_model)
                if success:
                    print(
                        f"✓ SystemValidator chat completion with {test_model} successful"
                    )
                else:
                    print(
                        f"⚠ SystemValidator chat completion with {test_model} failed: {result}"
                    )
            else:
                print("⚠ No valid test model found for chat completion")

    def test_integration_comprehensive(self):
        """Comprehensive integration test."""
        print("\n=== Comprehensive System Validation ===")

        results = {}

        # 1. Proxy connectivity
        try:
            self.test_proxy_connectivity()
            results["proxy"] = "PASS"
        except Exception as e:
            results["proxy"] = f"FAIL: {e}"

        # 2. Models availability
        try:
            self.test_models_availability()
            models = self._get_test_models()
            results["models"] = f"PASS: {len(models) if models else 0} models"
        except Exception as e:
            results["models"] = f"FAIL: {e}"

        # 3. Health check
        try:
            self.test_health_check()
            health_data = self._get_health_data()
            if health_data:
                healthy = health_data.get("healthy_count", 0)
                unhealthy = health_data.get("unhealthy_count", 0)
                results["health"] = f"PASS: {healthy}H/{unhealthy}U"
            else:
                results["health"] = "FAIL: No health data returned"
        except Exception as e:
            results["health"] = f"FAIL: {e}"

        # 4. Chat completion
        try:
            self.test_chat_completion()
            results["chat"] = "PASS"
        except Exception as e:
            results["chat"] = f"FAIL: {e}"

        # 5. SystemValidator (if available)
        if DEPENDENCIES_AVAILABLE and SystemValidatorClass:
            try:
                self.test_system_validator_class()
                results["validator"] = "PASS"
            except Exception as e:
                results["validator"] = f"FAIL: {e}"
        else:
            results["validator"] = "SKIP: Dependencies not available"

        # Print summary
        print("\n=== Test Results Summary ===")
        for test_name, result in results.items():
            status = (
                "✓"
                if result.startswith("PASS")
                else "⚠" if result.startswith("SKIP") else "✗"
            )
            print(f"{status} {test_name.capitalize()}: {result}")

        # Assert overall success
        failed_tests = [
            name for name, result in results.items() if result.startswith("FAIL")
        ]

        if failed_tests:
            pytest.fail(f"Tests failed: {', '.join(failed_tests)}")


def simple_validation_test():
    """Simple standalone validation test (without pytest)."""
    print("=== Simple System Validation Test ===\n")

    base_url = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    api_key = os.getenv("LITELLM_MASTER_KEY", "sk-1234")
    headers = {"Authorization": f"Bearer {api_key}"}

    tests_passed = 0
    total_tests = 4

    # 1. Proxy readiness
    print("1. Testing proxy readiness...")
    try:
        response = requests.get(f"{base_url}/health/readiness", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Proxy ready: {data.get('status', 'unknown')}")
            print(f"   ✓ Database: {data.get('db', 'unknown')}")
            print(f"   ✓ Version: {data.get('litellm_version', 'unknown')}")
            tests_passed += 1
        else:
            print(f"   ✗ Proxy not ready: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ✗ Connection error: {e}")

    # 2. Models availability
    print("\n2. Testing models availability...")
    try:
        response = requests.get(f"{base_url}/v1/models", headers=headers, timeout=15)
        if response.status_code == 200:
            data = response.json()
            models = data.get("data", [])
            print(f"   ✓ Found {len(models)} models:")
            for model in models[:3]:
                print(f"      - {model.get('id', 'unknown')}")
            if len(models) > 3:
                print(f"      ... and {len(models) - 3} more")
            tests_passed += 1
        else:
            print(f"   ✗ Models request failed: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ✗ Models request error: {e}")

    # 3. Health check
    print("\n3. Testing system health...")
    try:
        response = requests.get(f"{base_url}/health", headers=headers, timeout=60)
        if response.status_code == 200:
            data = response.json()
            healthy = data.get("healthy_count", 0)
            unhealthy = data.get("unhealthy_count", 0)
            print(f"   ✓ Health check: {healthy} healthy, {unhealthy} unhealthy")
            if unhealthy > 0:
                print("   ⚠ Some endpoints are unhealthy")
            tests_passed += 1
        else:
            print(f"   ✗ Health check failed: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ✗ Health check error: {e}")

    # 4. Chat completion test
    print("\n4. Testing chat completion...")
    try:
        # Get first available model
        response = requests.get(f"{base_url}/v1/models", headers=headers, timeout=15)
        if response.status_code == 200:
            models = response.json().get("data", [])
            if models:
                test_model = models[0].get("id")
                payload = {
                    "model": test_model,
                    "messages": [{"role": "user", "content": "Hello, test message."}],
                    "max_tokens": 50,
                    "temperature": 0.1,
                }

                response = requests.post(
                    f"{base_url}/v1/chat/completions",
                    headers={**headers, "Content-Type": "application/json"},
                    json=payload,
                    timeout=30,
                )

                if response.status_code == 200:
                    data = response.json()
                    content = (
                        data.get("choices", [{}])[0]
                        .get("message", {})
                        .get("content", "")
                    )
                    print(f"   ✓ Chat completion with {test_model} successful")
                    print(
                        f"   Response: {content[:80]}{'...' if len(content) > 80 else ''}"
                    )
                    tests_passed += 1
                else:
                    print(f"   ✗ Chat completion failed: HTTP {response.status_code}")
            else:
                print("   ✗ No models available for testing")
        else:
            print("   ✗ Could not get models for chat test")
    except Exception as e:
        print(f"   ✗ Chat completion error: {e}")

    # Summary
    print(f"\n=== Test Summary ===")
    print(f"Tests passed: {tests_passed}/{total_tests}")

    if tests_passed == total_tests:
        print("🎉 All tests passed! System is healthy.")
        return True
    else:
        print(
            f"⚠ {total_tests - tests_passed} test(s) failed. Check system configuration."
        )
        return False


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="LiteLLM System Validation Tests")
    parser.add_argument(
        "--simple", action="store_true", help="Run simple validation test"
    )
    parser.add_argument("--pytest", action="store_true", help="Run pytest tests")
    parser.add_argument(
        "--base-url", default="http://localhost:4000", help="LiteLLM base URL"
    )
    parser.add_argument("--api-key", help="LiteLLM API key")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")

    args = parser.parse_args()

    # Set environment variables if provided
    if args.base_url:
        os.environ["LITELLM_API_BASE"] = args.base_url
    if args.api_key:
        os.environ["LITELLM_MASTER_KEY"] = args.api_key

    if args.simple:
        # Run simple test
        success = simple_validation_test()
        sys.exit(0 if success else 1)
    elif args.pytest:
        # Run pytest
        pytest_args = [__file__]
        if args.verbose:
            pytest_args.append("-v")
        sys.exit(pytest.main(pytest_args))
    else:
        # Default: run simple test
        success = simple_validation_test()
        sys.exit(0 if success else 1)
