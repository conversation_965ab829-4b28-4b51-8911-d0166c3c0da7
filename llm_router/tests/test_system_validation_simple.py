#!/usr/bin/env python3
"""
Simple System Validation Script for testing SystemValidator without dependencies.
This script demonstrates how to use SystemValidator and can be used for testing.
"""

import os
import sys
import requests
import argparse


def simple_validation_test():
    """Простая проверка системы без импорта SystemValidator."""
    print("=== Простая проверка системы LiteLLM ===\n")
    
    base_url = "http://localhost:4000"
    api_key = os.getenv("LITELLM_MASTER_KEY", "sk-1234")
    headers = {"Authorization": f"Bearer {api_key}"}
    
    # 1. Проверка готовности прокси
    print("1. Проверка готовности прокси...")
    try:
        response = requests.get(f"{base_url}/health/readiness", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ Прокси готов: {data.get('status', 'unknown')}")
            print(f"   ✓ База данных: {data.get('db', 'unknown')}")
            print(f"   ✓ Версия LiteLLM: {data.get('litellm_version', 'unknown')}")
        else:
            print(f"   ✗ Прокси не готов: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ Ошибка подключения к прокси: {e}")
        return False
    
    # 2. Проверка списка моделей
    print("\n2. Проверка списка моделей...")
    try:
        response = requests.get(f"{base_url}/v1/models", headers=headers, timeout=15)
        if response.status_code == 200:
            data = response.json()
            models = data.get("data", [])
            print(f"   ✓ Найдено {len(models)} моделей:")
            for model in models[:3]:  # показать первые 3
                print(f"      - {model.get('id', 'unknown')}")
            if len(models) > 3:
                print(f"      ... и еще {len(models) - 3}")
        else:
            print(f"   ✗ Ошибка получения моделей: HTTP {response.status_code}")
            print(f"   Ответ: {response.text}")
            return False
    except Exception as e:
        print(f"   ✗ Ошибка запроса моделей: {e}")
        return False
    
    # 3. Тест простого запроса
    print("\n3. Тест простого запроса...")
    if models:
        test_model = models[0].get("id")
        try:
            payload = {
                "model": test_model,
                "messages": [{"role": "user", "content": "Привет! Это тест."}],
                "max_tokens": 50,
                "temperature": 0.1
            }
            response = requests.post(
                f"{base_url}/v1/chat/completions",
                headers={**headers, "Content-Type": "application/json"},
                json=payload,
                timeout=30
            )
            if response.status_code == 200:
                data = response.json()
                content = data.get("choices", [{}])[0].get("message", {}).get("content", "")
                print(f"   ✓ Тест с моделью {test_model} успешен")
                print(f"   Ответ: {content[:100]}{'...' if len(content) > 100 else ''}")
            else:
                print(f"   ✗ Тест с моделью {test_model} неудачен: HTTP {response.status_code}")
                print(f"   Ответ: {response.text}")
        except Exception as e:
            print(f"   ✗ Ошибка тестового запроса: {e}")
    
    # 4. Проверка здоровья системы
    print("\n4. Проверка здоровья системы...")
    try:
        response = requests.get(f"{base_url}/health", headers=headers, timeout=60)
        if response.status_code == 200:
            data = response.json()
            healthy = data.get("healthy_count", 0)
            unhealthy = data.get("unhealthy_count", 0)
            print(f"   ✓ Здоровых эндпоинтов: {healthy}")
            if unhealthy > 0:
                print(f"   ⚠ Нездоровых эндпоинтов: {unhealthy}")
                unhealthy_list = data.get("unhealthy_endpoints", [])
                for endpoint in unhealthy_list[:3]:
                    model = endpoint.get("model", "unknown")
                    error = endpoint.get("error", "unknown error")
                    print(f"      - {model}: {error}")
            else:
                print(f"   ✓ Все эндпоинты здоровы")
        else:
            print(f"   ✗ Ошибка проверки здоровья: HTTP {response.status_code}")
    except Exception as e:
        print(f"   ✗ Ошибка проверки здоровья: {e}")
    
    print("\n=== Проверка завершена ===")
    return True


def main():
    parser = argparse.ArgumentParser(description="Простая проверка системы LiteLLM")
    parser.add_argument("--base-url", default="http://localhost:4000", help="Базовый URL LiteLLM")
    parser.add_argument("--api-key", help="API ключ LiteLLM")
    args = parser.parse_args()
    
    # Установить переменные окружения если переданы
    if args.base_url:
        os.environ["LITELLM_API_BASE"] = args.base_url
    if args.api_key:
        os.environ["LITELLM_MASTER_KEY"] = args.api_key
    
    try:
        simple_validation_test()
    except KeyboardInterrupt:
        print("\nПроверка прервана пользователем")
        sys.exit(1)
    except Exception as e:
        print(f"Критическая ошибка: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
