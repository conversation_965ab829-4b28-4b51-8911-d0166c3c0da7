#!/usr/bin/env python3
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import argparse
import json
import logging
import os
import sys
import time
import traceback

from llm_router.logging_utils import get_logger
from llm_router.router_litellm.litellm_config_api import (
    LiteLLMConfigAPI,
    LiteLLMConfigAPIError,
)
import requests

"""
System Validation Script for LiteLLM Configuration.

This script validates that the LiteLLM system is working correctly
after configuration updates, including model availability and health checks.
"""

sys.path.append(str(Path(__file__).parent.parent))
logger = get_logger(__name__)
LITELLM_BASE_URL = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
TEST_TIMEOUT = 30
HEALTH_CHECK_RETRIES = 3


class SystemValidator:
    """Validates LiteLLM system state after configuration updates."""

    def __init__(self, base_url: str, api_key: Optional[str] = None):
        """
        Initialize the system validator.

        Args:
            base_url: LiteLLM proxy base URL
            api_key: API key for authentication
        """
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.headers = {}
        if self.api_key:
            self.headers["Authorization"] = f"Bearer {self.api_key}"
        logger.info(f"VALIDATOR: Initialized for {self.base_url}")

    def check_proxy_health(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Check if LiteLLM proxy is running and healthy.

        Returns:
            Tuple of (is_healthy, health_info)
        """
        logger.info("SYSTEM:  Checking proxy health")
        try:
            url = f"{self.base_url}/health/readiness"
            response = requests.get(url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                logger.info("SYSTEM:  Proxy readiness check passed")
                return (True, health_data)
            else:
                logger.warning(
                    f"VALIDATOR: Readiness check failed with status {response.status_code}"
                )
        except requests.RequestException as e:
            logger.error(f"VALIDATOR: Readiness check failed: {e}")
        try:
            url = f"{self.base_url}/health/liveliness"
            response = requests.get(url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                logger.info("SYSTEM:  Proxy liveliness check passed")
                return (True, {"status": "alive"})
            else:
                logger.error(
                    f"VALIDATOR: Liveliness check failed with status {response.status_code}"
                )
        except requests.RequestException as e:
            logger.error(f"VALIDATOR: Liveliness check failed: {e}")
        return (False, {})

    def get_models_list(self) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        Get list of available models from the proxy.

        Returns:
            Tuple of (success, models_list)
        """
        logger.info("SYSTEM:  Retrieving models list")
        try:
            url = f"{self.base_url}/v1/models"
            response = requests.get(url, headers=self.headers, timeout=15)
            if response.status_code == 200:
                data = response.json()
                models = data.get("data", [])
                logger.info(f"VALIDATOR: Found {len(models)} models")
                return (True, models)
            else:
                logger.error(
                    f"VALIDATOR: Models list failed with status {response.status_code}"
                )
                logger.error(f"Response: {response.text}")
                return (False, [])
        except requests.RequestException as e:
            logger.error(f"VALIDATOR: Models list request failed: {e}")
            return (False, [])

    def get_model_info(self) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        Get detailed model information from the proxy.

        Returns:
            Tuple of (success, model_info_list)
        """
        logger.info("SYSTEM:  Retrieving detailed model information")
        try:
            url = f"{self.base_url}/model/info"
            response = requests.get(url, headers=self.headers, timeout=15)
            if response.status_code == 200:
                data = response.json()
                models = data.get("data", [])
                logger.info(f"VALIDATOR: Retrieved info for {len(models)} models")
                return (True, models)
            else:
                logger.error(
                    f"VALIDATOR: Model info failed with status {response.status_code}"
                )
                logger.error(f"Response: {response.text}")
                return (False, [])
        except requests.RequestException as e:
            logger.error(f"VALIDATOR: Model info request failed: {e}")
            return (False, [])

    def test_chat_completion(self, model_name: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Test a simple chat completion with a specific model.

        Args:
            model_name: Name of the model to test

        Returns:
            Tuple of (success, response_info)
        """
        logger.info(f"VALIDATOR: Testing chat completion with model {model_name}")
        try:
            url = f"{self.base_url}/v1/chat/completions"
            payload = {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello, this is a test message. Please respond with 'Test successful'.",
                    }
                ],
                "max_tokens": 50,
                "temperature": 0.1,
            }
            response = requests.post(
                url,
                headers={**self.headers, "Content-Type": "application/json"},
                json=payload,
                timeout=TEST_TIMEOUT,
            )
            if response.status_code == 200:
                data = response.json()
                content = (
                    data.get("choices", [{}])[0].get("message", {}).get("content", "")
                )
                logger.info(f"VALIDATOR: Chat completion successful for {model_name}")
                logger.debug(f"Response content: {content[:100]}...")
                return (
                    True,
                    {
                        "model": model_name,
                        "response_length": len(content),
                        "usage": data.get("usage", {}),
                    },
                )
            else:
                logger.error(
                    f"VALIDATOR: Chat completion failed for {model_name}: {response.status_code}"
                )
                logger.error(f"Response: {response.text}")
                return (False, {"error": response.text})
        except requests.RequestException as e:
            logger.error(
                f"VALIDATOR: Chat completion request failed for {model_name}: {e}"
            )
            return (False, {"error": str(e)})

    def run_health_checks(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Run comprehensive health checks on all models.

        Returns:
            Tuple of (all_healthy, health_results)
        """
        logger.info("SYSTEM:  Running comprehensive health checks")
        try:
            url = f"{self.base_url}/health"
            response = requests.get(url, headers=self.headers, timeout=60)
            if response.status_code == 200:
                health_data = response.json()
                healthy_count = health_data.get("healthy_count", 0)
                unhealthy_count = health_data.get("unhealthy_count", 0)
                logger.info(
                    f"VALIDATOR: Health check completed - {healthy_count} healthy, {unhealthy_count} unhealthy"
                )
                if unhealthy_count > 0:
                    logger.warning("SYSTEM:  Some endpoints are unhealthy")
                    unhealthy = health_data.get("unhealthy_endpoints", [])
                    for endpoint in unhealthy[:3]:
                        model = endpoint.get("model", "unknown")
                        error = endpoint.get("error", "unknown error")
                        logger.warning(f"   Unhealthy: {model} - {error}")
                return (unhealthy_count == 0, health_data)
            else:
                logger.error(
                    f"VALIDATOR: Health check failed with status {response.status_code}"
                )
                return (False, {"error": response.text})
        except requests.RequestException as e:
            logger.error(f"VALIDATOR: Health check request failed: {e}")
            return (False, {"error": str(e)})


def print_validation_summary(results: Dict[str, Any]) -> None:
    """Print a formatted summary of validation results."""
    print("\n" + "=" * 80)
    print("LITELLM SYSTEM VALIDATION SUMMARY")
    print("=" * 80)
    if results.get("proxy_healthy"):
        print("[OK] Proxy Health: HEALTHY")
    else:
        print("[ERROR] Proxy Health: UNHEALTHY")
    models_count = len(results.get("models", []))
    if models_count > 0:
        print(f"[OK] Models Available: {models_count} models found")
    else:
        print("[ERROR] Models Available: No models found")
    health_results = results.get("health_check_results", {})
    if health_results:
        healthy_count = health_results.get("healthy_count", 0)
        unhealthy_count = health_results.get("unhealthy_count", 0)
        total_count = healthy_count + unhealthy_count
        if unhealthy_count == 0 and healthy_count > 0:
            print(f"[OK] Health Checks: All {healthy_count} endpoints healthy")
        elif unhealthy_count > 0:
            print(
                f"[WARNING] Health Checks: {healthy_count}/{total_count} endpoints healthy"
            )
        else:
            print("[ERROR] Health Checks: No healthy endpoints found")
    # Router settings now use constants instead of configuration
    print("[OK] Router Settings: Using constants (simplified configuration)")
    test_results = results.get("test_completion_results", [])
    successful_tests = [r for r in test_results if r.get("success")]
    if successful_tests:
        print(
            f"[OK] Test Completions: {len(successful_tests)}/{len(test_results)} successful"
        )
    elif test_results:
        print(f"[ERROR] Test Completions: 0/{len(test_results)} successful")
    print("=" * 80)
    if not results.get("proxy_healthy"):
        print("\n[CRITICAL] LiteLLM proxy is not responding properly")
        print("   -> Check if the proxy service is running")
        print("   -> Verify the proxy URL and API key")
    if models_count == 0:
        print("\n[WARNING] No models found")
        print("   -> Run: ./run.sh config update-models litellm.config.yaml")
        print("   -> Check your configuration file")
    health_results = results.get("health_check_results", {})
    if health_results.get("unhealthy_count", 0) > 0:
        print("\n[WARNING] Some model endpoints are unhealthy")
        print("   -> Check API keys and endpoint configurations")
        print("   -> Review unhealthy endpoints in the health check results")
    # Router settings warnings removed - now using constants


def main():
    """Main validation function."""
    parser = argparse.ArgumentParser(
        description="Validate LiteLLM system configuration"
    )
    parser.add_argument("--api-key", help="LiteLLM Master API key")
    parser.add_argument(
        "--base-url", default=LITELLM_BASE_URL, help="LiteLLM proxy base URL"
    )
    parser.add_argument(
        "--test-model", help="Specific model to test with chat completion"
    )
    parser.add_argument(
        "--skip-health-check",
        action="store_true",
        help="Skip comprehensive health checks",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )
    args = parser.parse_args()
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    api_key = args.api_key or os.getenv("LITELLM_MASTER_KEY")
    if not api_key:
        logger.warning(
            "SYSTEM:  No API key provided - some endpoints may be inaccessible"
        )
    validator = SystemValidator(base_url=args.base_url, api_key=api_key)
    results = {}
    try:
        print("Starting LiteLLM system validation...")
        print("\n1. Checking proxy health...")
        proxy_healthy, health_info = validator.check_proxy_health()
        results["proxy_healthy"] = proxy_healthy
        results["health_info"] = health_info
        if proxy_healthy:
            print("   [OK] Proxy is responding")
        else:
            print("   [ERROR] Proxy is not responding properly")
            print_validation_summary(results)
            return
        print("\n2. Retrieving models list...")
        models_success, models = validator.get_models_list()
        results["models"] = models if models_success else []
        if models_success and models:
            print(f"   [OK] Found {len(models)} models")
            for model in models[:5]:
                model_id = model.get("id", "unknown")
                print(f"      - {model_id}")
            if len(models) > 5:
                print(f"      ... and {len(models) - 5} more")
        else:
            print("   [WARNING] No models found or error retrieving models")
        print("\n3. Retrieving detailed model information...")
        info_success, model_info = validator.get_model_info()
        results["model_info"] = model_info if info_success else []
        if info_success and model_info:
            print(f"   [OK] Retrieved info for {len(model_info)} models")
        else:
            print("   [WARNING] Could not retrieve detailed model information")
        print("\n4. Router settings validation skipped (using constants)")
        # Router settings validation removed - using constants instead
        results["router_settings_valid"] = True
        results["router_settings"] = {"status": "using_constants"}
        print("   [OK] Router settings using constants")
        if not args.skip_health_check:
            print("\n5. Running comprehensive health checks...")
            health_success, health_results = validator.run_health_checks()
            results["health_check_results"] = health_results
            if health_success:
                print("   [OK] All endpoints are healthy")
            else:
                healthy_count = health_results.get("healthy_count", 0)
                unhealthy_count = health_results.get("unhealthy_count", 0)
                if healthy_count > 0:
                    print(
                        f"   [WARNING] {healthy_count} healthy, {unhealthy_count} unhealthy endpoints"
                    )
                else:
                    print("   [ERROR] No healthy endpoints found")
        if args.test_model:
            print(f"\n6. Testing chat completion with {args.test_model}...")
            test_success, test_result = validator.test_chat_completion(args.test_model)
            results["test_completion_results"] = [
                {
                    "success": test_success,
                    "model": args.test_model,
                    "result": test_result,
                }
            ]
            if test_success:
                print("   [OK] Chat completion test successful")
            else:
                print("   [ERROR] Chat completion test failed")
        elif models:
            test_model = models[0].get("id")
            if test_model:
                print(f"\n6. Testing chat completion with {test_model}...")
                test_success, test_result = validator.test_chat_completion(test_model)
                results["test_completion_results"] = [
                    {
                        "success": test_success,
                        "model": test_model,
                        "result": test_result,
                    }
                ]
                if test_success:
                    print("   [OK] Chat completion test successful")
                else:
                    print("   [ERROR] Chat completion test failed")
        print_validation_summary(results)
        critical_issues = [
            not results.get("proxy_healthy"),
            len(results.get("models", [])) == 0,
        ]
        if any(critical_issues):
            logger.error(
                "SYSTEM:  Critical issues found - system may not be functional"
            )
            sys.exit(1)
        else:
            logger.info("SYSTEM:  Validation completed successfully")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\nValidation cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"VALIDATOR: Fatal error during validation: {e}")
        logger.debug(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
