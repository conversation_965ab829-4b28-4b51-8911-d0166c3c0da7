#!/bin/bash

# Configuration Validation Module
# Handles configuration validation and database checks

# Load constants first, then common functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
source "$SCRIPT_DIR/scripts/common/constants.sh"
source "$SCRIPT_DIR/scripts/common/functions.sh"
source "$SCRIPT_DIR/scripts/common/paths.sh"

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

# Check router configuration locally
validation_check() {
    log_info "Checking router configuration locally..."
    
    execute_local_script_by_key "config_manager" "Checking router configuration locally"
}

# Check router_settings stored in database
validation_check_db() {
    log_info "Checking router_settings stored in database..."
    
    execute_local_script_by_key "check_db_router_settings" "Checking database router settings"
}

# Comprehensive validation of entire system
validation_check_system() {
    log_info "Running comprehensive system validation..."
    
    echo "=== System Validation Report ==="
    echo ""
    
    # Check 1: Configuration files
    echo "1. Configuration Files:"
    local config_files=("litellm.config.yaml" ".env" "docker-compose.yml")
    local config_ok=true
    
    for file in "${config_files[@]}"; do
        if [[ -f "$SCRIPT_DIR/$file" ]]; then
            echo "  OK $file exists"
        else
            echo "  FAIL $file missing"
            config_ok=false
        fi
    done
    echo ""
    
    # Check 2: Environment variables
    echo "2. Environment Variables:"
    local env_vars=("LITELLM_MASTER_KEY" "DATABASE_URL")
    local env_ok=true
    
    for var in "${env_vars[@]}"; do
        if [[ -n "${!var}" ]]; then
            echo "  OK $var is set"
        else
            echo "  FAIL $var is not set"
            env_ok=false
        fi
    done
    echo ""
    
    # Check 3: Services
    echo "3. Service Connectivity:"
    local services_ok=true
    
    for service in "${SERVICES[@]}"; do
        local port="${SERVICE_PORTS[$service]}"
        if [[ -n "$port" ]]; then
            if nc -z localhost "$port" 2>/dev/null; then
                echo "  OK $service (port $port) - responding"
            else
                echo "  FAIL $service (port $port) - not responding"
                services_ok=false
            fi
        fi
    done
    echo ""
    
    # Check 4: Configuration validation
    echo "4. Configuration Validation:"
    if validation_check >/dev/null 2>&1; then
        echo "  OK Configuration validation passed"
    else
        echo "  FAIL Configuration validation failed"
        config_ok=false
    fi
    echo ""
    
    # Check 5: Database validation
    echo "5. Database Validation:"
    if validation_check_db >/dev/null 2>&1; then
        echo "  OK Database validation passed"
    else
        echo "  FAIL Database validation failed"
        services_ok=false
    fi
    echo ""
    
    # Summary
    echo "=== Validation Summary ==="
    local overall_ok=true
    
    if [[ "$config_ok" == true ]]; then
        echo "  OK Configuration: OK"
    else
        echo "  FAIL Configuration: FAILED"
        overall_ok=false
    fi
    
    if [[ "$env_ok" == true ]]; then
        echo "  OK Environment: OK"
    else
        echo "  FAIL Environment: FAILED"
        overall_ok=false
    fi
    
    if [[ "$services_ok" == true ]]; then
        echo "  OK Services: OK"
    else
        echo "  FAIL Services: FAILED"
        overall_ok=false
    fi
    
    echo ""
    
    # Check 6: LiteLLM System Validation (using validate_system.py)
    echo "6. LiteLLM System Validation:"
    if execute_local_script_by_key "validate_system" "Running LiteLLM system validation" --skip-health-check >/dev/null 2>&1; then
        echo "  OK LiteLLM system validation passed"
    else
        echo "  FAIL LiteLLM system validation failed (may require dependencies)"
        services_ok=false
    fi
    
    # Calculate failed checks
    local failed_checks=0
    if [[ "$config_ok" != true ]]; then
        ((failed_checks++))
    fi
    if [[ "$env_ok" != true ]]; then
        ((failed_checks++))
    fi
    if [[ "$services_ok" != true ]]; then
        ((failed_checks++))
    fi
    
    if [ $failed_checks -eq 0 ]; then
        echo "[SUCCESS] System validation PASSED - All checks successful!"
        return 0
    else
        echo "[WARNING] System validation FAILED - Please address the issues above"
        return 1
    fi
}

# Validate specific configuration file
validation_check_file() {
    local config_file="$1"
    
    if [[ -z "$config_file" ]]; then
        show_error_with_usage "Configuration file required" "./run.sh config validation check-file CONFIG_FILE"
        return 1
    fi
    
    validate_file_exists "$config_file" "Configuration file"
    
    log_info "Validating configuration file: $config_file"
    
    # Check YAML syntax
    echo "Checking YAML syntax..."
    if python3 -c "import yaml; yaml.safe_load(open('$config_file'))" 2>/dev/null; then
        echo "  OK YAML syntax is valid"
    else
        echo "  FAIL YAML syntax is invalid"
        return 1
    fi
    
    # Check required sections
    echo "Checking required sections..."
    local required_sections=("model_list")
    local sections_ok=true
    
    for section in "${required_sections[@]}"; do
        if grep -q "^$section:" "$config_file"; then
            echo "  OK $section section found"
        else
            echo "  FAIL $section section missing"
            sections_ok=false
        fi
    done
    
    # Note about router_settings removal
    if grep -q "^router_settings:" "$config_file"; then
        echo "  WARNING router_settings section found but deprecated - system uses constants"
    else
        echo "  OK router_settings section not needed (system uses constants)"
    fi
    
    if [[ "$sections_ok" == true ]]; then
        echo "Configuration file validation passed!"
        return 0
    else
        echo "Configuration file validation failed!"
        return 1
    fi
}

# Test database connectivity
validation_test_db() {
    log_info "Testing database connectivity..."
    
    # Check if database service is running
    if ! nc -z localhost 5432 2>/dev/null; then
        echo "ERROR: Database service is not responding on port 5432"
        echo "Start the database with: ./run.sh docker up db"
        return 1
    fi
    
    echo "Database service is responding on port 5432"
    
    # Test actual database connection
    echo "Testing database connection..."
    if execute_local_script_by_key "check_db_router_settings" "Testing database connection"; then
        echo "OK Database connection test passed"
        return 0
    else
        echo "FAIL Database connection test failed"
        return 1
    fi
}

# =============================================================================
# HELP FUNCTION
# =============================================================================

show_validation_help() {
    cat << EOF
Configuration Validation Commands

USAGE:
  ./run.sh config validation [COMMAND] [OPTIONS]

BASIC COMMANDS:
  check                 Check router configuration locally
  check-db              Check router_settings stored in database
  check-system          Run comprehensive system validation
  check-file FILE       Validate specific configuration file

CONNECTIVITY TESTS:
  test-db               Test database connectivity and access

EXAMPLES:
  ./run.sh config validation check                     # Check local configuration
  ./run.sh config validation check-db                  # Check database settings
  ./run.sh config validation check-system              # Full system validation
  ./run.sh config validation check-file my-config.yaml # Validate specific file
  ./run.sh config validation test-db                   # Test database connection

VALIDATION CHECKS:

1. Configuration Files:
   - Existence of required files (litellm.config.yaml, .env, docker-compose.yml)
   - YAML syntax validation
   - Required sections validation

2. Environment Variables:
   - LITELLM_MASTER_KEY presence
   - DATABASE_URL configuration
   - Other required environment variables

3. Service Connectivity:
   - LiteLLM service (port 4000)
   - Database service (port 5432)
   - Prometheus service (port 9090)

4. Database Validation:
   - Connection test
   - Router settings validation
   - Model configuration checks

NOTES:
  - check runs locally without requiring services
  - check-db requires database service to be running
  - check-system provides comprehensive validation report
  - check-file validates YAML syntax and structure
  - test-db specifically tests database connectivity

TROUBLESHOOTING:
  - If services fail: ./run.sh docker up
  - If config fails: ./run.sh config check
  - If database fails: ./run.sh config validation test-db

EOF
}

# =============================================================================
# COMMAND DISPATCHER
# =============================================================================

handle_validation_command() {
    local command="$1"
    shift
    
    # Initialize environment
    init_constants
    init_paths
    
    case "$command" in
        check)
            validation_check
            ;;
        check-db)
            validation_check_db
            ;;
        check-system)
            validation_check_system
            ;;
        check-file)
            validation_check_file "$@"
            ;;
        test-db)
            validation_test_db
            ;;
        help|--help|-h)
            show_validation_help
            ;;
        *)
            show_error_with_usage "Unknown validation command: $command" "./run.sh config validation help"
            return 1
            ;;
    esac
}

# If script is called directly, handle the command
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    handle_validation_command "$@"
fi 